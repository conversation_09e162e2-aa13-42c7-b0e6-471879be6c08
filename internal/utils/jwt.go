package utils

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// QRCodeInfo holds custom payload data for QR-based JWTs.
type QRCodeInfo struct {
	ReqDeviceId string    `json:"req_device_id"`
	BranchId    int       `json:"branch_id"`
	ClientId    string    `json:"client_id"`
	ConnType    string    `json:"conn_type"`
	GenTime     time.Time `json:"gen_time"`
	ExpTime     time.Time `json:"exp_time"`
}

// Claims defines the custom and standard JWT claims, embedding QRCodeInfo.
type Claims struct {
	QRInfo QRCodeInfo `json:"qr_info"`
	jwt.RegisteredClaims
}

// InitJWTSecret initializes the JWT secret key from configuration
// Call once at startup with the secret from config

// GenerateToken signs a JWT containing QRCodeInfo, valid for ttl duration.
func GenerateJWTToken(info QRCodeInfo, ttl time.Duration, jwtKey []byte) (string, error) {
	// Set issued, expiration, and not-before
	now := time.Now()
	info.GenTime = now
	info.ExpTime = now.Add(ttl)

	claims := Claims{
		QRInfo: info,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    "your-app-name",
			Audience:  []string{"your-api"},
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(info.ExpTime),
			ID:        info.ClientId + "-" + now.Format(time.RFC3339Nano),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtKey)
}

// ValidateToken parses and validates a JWT string, returning Claims if valid.
func ValidateToken(tokenStr string, jwtKey []byte) (*Claims, error) {
	// Input validation
	if tokenStr == "" {
		return nil, errors.New("token cannot be empty")
	}

	// Check for reasonable token length to prevent DoS
	if len(tokenStr) > 8192 { // 8KB limit
		return nil, errors.New("token too long")
	}

	// Check for basic JWT structure (3 parts separated by dots)
	parts := strings.Split(tokenStr, ".")
	if len(parts) != 3 {
		return nil, errors.New("malformed token: invalid structure")
	}

	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenStr, claims, func(t *jwt.Token) (interface{}, error) {
		if _, ok := t.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", t.Header["alg"])
		}
		return jwtKey, nil
	})
	if err != nil {
		// Handle specific JWT errors with better messages
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, errors.New("token has expired")
		}
		if errors.Is(err, jwt.ErrTokenNotValidYet) {
			return nil, errors.New("token not valid yet (clock skew detected)")
		}
		if errors.Is(err, jwt.ErrTokenMalformed) {
			return nil, errors.New("malformed token")
		}
		if errors.Is(err, jwt.ErrTokenSignatureInvalid) {
			return nil, errors.New("invalid token signature")
		}
		return nil, fmt.Errorf("token validation failed: %w", err)
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	// Validate QRInfo fields are not empty
	if claims.QRInfo.ReqDeviceId == "" {
		return nil, errors.New("token missing required device ID")
	}
	if claims.QRInfo.ClientId == "" {
		return nil, errors.New("token missing required client ID")
	}
	if claims.QRInfo.ConnType == "" {
		return nil, errors.New("token missing required connection type")
	}

	// Validate connection type
	validConnTypes := map[string]bool{
		"qr-in":  true,
		"qr-out": true,
	}
	if !validConnTypes[claims.QRInfo.ConnType] {
		return nil, fmt.Errorf("invalid connection type: %s", claims.QRInfo.ConnType)
	}

	return claims, nil
}

// ExtractQRInfo validates the token and extracts QRCodeInfo data.
func ExtractQRInfo(tokenStr string, jwtKey []byte) (*QRCodeInfo, error) {
	claims, err := ValidateToken(tokenStr, jwtKey)
	if err != nil {
		return nil, err
	}
	return &claims.QRInfo, nil
}

func GenerateTokenForToday(info QRCodeInfo, jwtKey []byte) (string, error) {
	now := time.Now()
	// Calculate next midnight
	nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
	ttl := nextMidnight.Sub(now)
	return GenerateJWTToken(info, ttl, jwtKey)
}
