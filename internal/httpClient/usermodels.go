package http

type CreateDoorLockUserParams struct {
	AttendeeID     int64  `json:"attendee_id"`
	UserID         int64  `json:"user_id"`
	Username       string `json:"username"`
	BranchID       int64  `json:"branch_id"`
	SubscriptionID int64  `json:"subscription_id"`
	LastIn         string `json:"last_in"`
	LastOut        string `json:"last_out"`
	CurrentState   string `json:"current_state"`
}

// func (S *CreateDoorLockUserParams) convertToDB() (*database.InsertDoorLockUserParams, error) {
// 	var last_in sql.NullTime
// 	Time, err := time.Parse(time.RFC3339, S.LastIn)
// 	if err != nil {
// 		return nil, err
// 	}
// 	last_in.Time = Time
// 	last_in.Valid = true
//
// 	var last_out sql.NullTime
// 	Time, err = time.Parse(time.RFC3339, S.LastOut)
// 	if err != nil {
// 		return nil, err
// 	}
// 	last_out.Time = Time
// 	last_out.Valid = true
//
// 	var current_state sql.NullString
// 	current_state.String = S.CurrentState
// 	current_state.Valid = true
//
// 	return &database.InsertDoorLockUserParams{
// 		UserID:         S.UserID,
// 		Username:       S.Username,
// 		BranchID:       S.BranchID,
// 		SubscriptionID: S.SubscriptionID,
// 		LastIn:         last_in,
// 		LastOut:        last_out,
// 		CurrentState:   current_state,
// 	}, nil
//
// }
