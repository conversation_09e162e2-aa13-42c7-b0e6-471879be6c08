package http

import (
	"crypto/rand"
	"database/sql"
	"encoding/base64"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/ws"
	"github.com/labstack/echo/v4"
)

// EmergencyExitOperation represents a rollback-able operation
type EmergencyExitOperation struct {
	Description string
	Rollback    func() error
}

func EmergenceyExitFunc(H *HttpRoutes, client *ws.Client, door *ws.DoorLockType, qrId string, reqQrClient string) error {
	// Validate inputs
	if H == nil || client == nil || door == nil {
		log.Printf("[ERROR @ EmergenceyExitFunc] >> Invalid parameters: H=%v, client=%v, door=%v", H, client, door)
		return fmt.Errorf("invalid parameters provided to EmergenceyExitFunc")
	}

	// Track operations for rollback capability
	var operations []EmergencyExitOperation
	var errors []string
	successCount := 0
	failureCount := 0

	// Capture current state for potential rollback
	originalState := client.GetState()
	log.Printf("[EmergenceyExitFunc] >> Original client state: processing=%v, status=%v", originalState.Processing, originalState.Status)

	// Validate current state consistency
	if !client.IsStateConsistent() {
		log.Printf("[WARNING @ EmergenceyExitFunc] >> Client state is inconsistent before cleanup")
	}

	// Step 1: Reset client state atomically with rollback capability
	err := client.TransitionToIdle()
	if err != nil {
		log.Printf("[ERROR @ EmergenceyExitFunc] >> Failed to transition client to idle: %v", err)
		return fmt.Errorf("failed to reset client state: %w", err)
	}

	// Add rollback operation for client state
	operations = append(operations, EmergencyExitOperation{
		Description: "Reset client state",
		Rollback: func() error {
			return client.SetStateAtomic(originalState.Processing, originalState.Status)
		},
	})

	log.Printf("[EmergenceyExitFunc] >> Client state reset to IDLE atomically")

	// Step 2: Get QR devices safely and validate before operations
	qrDevices := door.GetQRDevices()
	if len(qrDevices) == 0 {
		log.Printf("[EmergenceyExitFunc] >> No QR devices found for door")
	} else {
		log.Printf("[EmergenceyExitFunc] >> Processing %d QR devices", len(qrDevices))
	}

	// Step 3: Send reset messages to all QR clients with proper error handling
	var successfulQRClients []string
	for _, cli := range qrDevices {
		qrcli, exists := H.hub.GetClient(cli.Id)
		if !exists {
			failureCount++
			errMsg := fmt.Sprintf("QR client %s not found in hub", cli.Id)
			errors = append(errors, errMsg)
			log.Printf("[ERROR @ EmergenceyExitFunc] >> %s", errMsg)
			continue
		}

		if qrcli == nil {
			failureCount++
			errMsg := fmt.Sprintf("QR client %s is nil", cli.Id)
			errors = append(errors, errMsg)
			log.Printf("[ERROR @ EmergenceyExitFunc] >> %s", errMsg)
			continue
		}

		err := qrcli.SendMsg(types.MessageType{
			MsgType: "set-idel",
		})
		if err != nil {
			failureCount++
			errMsg := fmt.Sprintf("Failed to send reset message to QR client %s: %v", cli.Id, err)
			errors = append(errors, errMsg)
			log.Printf("[ERROR @ EmergenceyExitFunc] >> %s", errMsg)
		} else {
			successCount++
			successfulQRClients = append(successfulQRClients, cli.Id)
			log.Printf("[EmergenceyExitFunc] >> Successfully sent reset message to QR client %s", cli.Id)
		}
	}

	// Step 4: Send generate-new message to requesting QR client with error handling
	if reqQrClient != "" {
		err := H.hub.SendToClient(reqQrClient, types.MessageType{
			MsgType: "generate-new",
		})
		if err != nil {
			errMsg := fmt.Sprintf("Failed to send generate-new message to requesting client %s: %v", reqQrClient, err)
			errors = append(errors, errMsg)
			log.Printf("[ERROR @ EmergenceyExitFunc] >> %s", errMsg)
		} else {
			log.Printf("[EmergenceyExitFunc] >> Successfully sent generate-new message to client %s", reqQrClient)
		}
	} else {
		log.Printf("[EmergenceyExitFunc] >> No requesting QR client specified")
	}

	// Step 5: Evaluate if rollback is needed
	criticalFailureThreshold := 0.5       // If more than 50% of operations fail, consider rollback
	totalOperations := len(qrDevices) + 1 // QR operations + generate-new
	if totalOperations > 0 && float64(failureCount)/float64(totalOperations) > criticalFailureThreshold {
		log.Printf("[EmergenceyExitFunc] >> Critical failure threshold exceeded (%d/%d failed), initiating rollback", failureCount, totalOperations)

		// Perform rollback
		for i := len(operations) - 1; i >= 0; i-- {
			op := operations[i]
			if rollbackErr := op.Rollback(); rollbackErr != nil {
				log.Printf("[ERROR @ EmergenceyExitFunc] >> Rollback failed for '%s': %v", op.Description, rollbackErr)
			} else {
				log.Printf("[EmergenceyExitFunc] >> Rollback successful for '%s'", op.Description)
			}
		}

		return fmt.Errorf("emergency exit failed with critical errors, rollback performed: %v", errors)
	}

	// Step 6: Log comprehensive summary
	log.Printf("[EmergenceyExitFunc] >> Cleanup completed: %d success, %d failures", successCount, failureCount)

	// Validate final state consistency
	if !client.IsStateConsistent() {
		log.Printf("[ERROR @ EmergenceyExitFunc] >> Client state is inconsistent after cleanup")
		errors = append(errors, "client state inconsistent after cleanup")
	}

	if len(errors) > 0 {
		log.Printf("[EmergenceyExitFunc] >> Errors encountered: %v", errors)
		return fmt.Errorf("emergency exit completed with %d errors: %v", len(errors), errors)
	}

	log.Printf("[EmergenceyExitFunc] >> Emergency exit completed successfully")
	return nil
}

func (H *HttpRoutes) OpenDoor(c echo.Context) error {
	var req types.DoorLockAccessReq
	err := c.Bind(&req)
	if err != nil {
		log.Printf("[ERROR - OpenDoor] Request binding failed: %v", err)
		return c.JSON(http.StatusBadRequest, "request is not valid")
	}

	log.Printf("[OpenDoor] Processing request for user ID: %d, QR code length: %d", req.User.ID, len(req.QRcode))

	// Validate JWT token and extract QR code info
	claim, err := utils.ValidateToken(req.QRcode)
	if err != nil {
		log.Printf("[ERROR - OpenDoor] Invalid QR token: %v", err)
		return c.JSON(http.StatusUnauthorized, "invalid qr code")
	}
	qrData := claim.QRInfo

	log.Printf("[OpenDoor] QR Data extracted - ReqDeviceId: %s, ClientId: %s, ConnType: %s, BranchId: %d",
		qrData.ReqDeviceId, qrData.ClientId, qrData.ConnType, qrData.BranchId)

	// CRITICAL FIX: Validate that the target device is connected
	client, exists := H.hub.GetClient(qrData.ReqDeviceId)
	if !exists {
		log.Printf("[ERROR - OpenDoor] Target device %s not connected", qrData.ReqDeviceId)
		return c.JSON(http.StatusBadRequest, "target device not connected")
	}

	// Validate device is not banned
	if client.IsBanned() {
		log.Printf("[ERROR - OpenDoor] Device %s is banned", qrData.ReqDeviceId)
		return c.JSON(http.StatusUnauthorized, "device is banned")
	}

	// Additional validation: ensure client is not nil (redundant check removed)
	log.Printf("[OpenDoor] Target device %s found and validated", qrData.ReqDeviceId)

	// check if the client is in use
	if client.GetProcessing() {
		log.Printf("[ERROR @ 77] >> %v", "client processing")
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "client is already in use"))
	}

	log.Printf("[client data] >> %v", client)

	doorData, ok := client.Data.(*ws.DoorLockType)
	if !ok {
		log.Printf("[ERROR @ 85] >> %v", "door data")
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "client data is not valid"))
	}
	log.Printf("[doordata data] >> %v", doorData)

	// set the QR client to processing
	for _, cli := range doorData.GetQRDevices() {
		// TODO: there should be a error check here
		err := H.hub.SendToClient(cli.Id, types.MessageType{
			MsgType: "processing",
		})
		if err != nil {
			client.SetProcessing(false)
			log.Printf("[ERROR @ 99] >> %v", err)
			return c.JSON(http.StatusInternalServerError, "failed to send processing message")
		}
	}

	// -------------------------------------------------safe area start-------------------------------------------------

	client.SetProcessing(true)
	client.SetStatus(code.QRPROCESSING)

	// if !slices.Contains(doorData.QRDevices, qrData.ClientId) {
	// 	return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "client is not a qr device"))
	// }

	// CRITICAL FIX: Validate QR client exists and is connected
	qrClient, qrClientExists := H.hub.GetClient(qrData.ClientId)
	if !qrClientExists {
		log.Printf("[ERROR - OpenDoor] QR client %s not connected", qrData.ClientId)
		if err := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); err != nil {
			log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", err)
		}
		return c.JSON(http.StatusBadRequest, "QR client not connected")
	}

	// Validate QR client is not banned
	if qrClient.IsBanned() {
		log.Printf("[ERROR - OpenDoor] QR client %s is banned", qrData.ClientId)
		if err := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); err != nil {
			log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", err)
		}
		return c.JSON(http.StatusUnauthorized, "QR client is banned")
	}

	// check client type inside the door data
	found := false
	for _, qr := range doorData.GetQRDevices() {
		if qr.Id == qrData.ClientId {
			found = true
			if qr.QRType != qrData.ConnType {
				log.Printf("[ERROR - OpenDoor] QR type mismatch: expected %s, got %s", qr.QRType, qrData.ConnType)
				if err := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); err != nil {
					log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", err)
				}
				return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "QR type mismatch"))
			}
			break
		}
	}

	if !found {
		log.Printf("[ERROR - OpenDoor] QR client %s not found in door's QR devices list", qrData.ClientId)
		if err := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); err != nil {
			log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", err)
		}
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "QR client not registered with door"))
	}

	log.Printf("[OpenDoor] QR client %s validated successfully", qrData.ClientId)

	doorData.SetProcessingQR(qrData.ClientId)

	var userID int64
	// check the user
	userID, err = H.query.GetAttendeeIDByUserID(c.Request().Context(), int64(req.User.ID))
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {

			err := H.query.InsertDoorLockUser(c.Request().Context(), database.InsertDoorLockUserParams{
				UserID:    int64(req.User.ID),
				Username:  req.User.Name,
				BranchID:  int64(qrData.BranchId),
				Email:     sql.NullString{String: req.User.Email, Valid: true},
				Role:      sql.NullString{String: req.User.Role, Valid: true},
				Phone:     sql.NullString{String: req.User.Phone, Valid: true},
				Nic:       sql.NullString{String: req.User.NIC, Valid: true},
				AvatarUrl: sql.NullString{String: req.User.AvatarURL, Valid: true},
			})
			if err != nil {
				log.Printf("[ERROR 147: OpenDoor] >> %v", err)
				if exitErr := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); exitErr != nil {
					log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", exitErr)
				}
				return c.JSON(http.StatusInternalServerError, "failed to insert user")
			}

			userID, err = H.query.GetAttendeeIDByUserID(c.Request().Context(), int64(req.User.ID))
			if err != nil {
				log.Printf("[ERROR 154: OpenDoor] >> %v", err)
				if exitErr := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); exitErr != nil {
					log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", exitErr)
				}
				return c.JSON(http.StatusInternalServerError, "failed to get user id")
			}

		} else {
			log.Printf("[ERROR 160: OpenDoor] >> %v", err)
			if exitErr := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); exitErr != nil {
				log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", exitErr)
			}
			return c.JSON(http.StatusBadRequest, "user not found")
		}
	}

	timeNow := time.Now()
	dateOnly := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, time.UTC)

	if qrData.ConnType == "qr-in" {
		isAlreadyIn, err := H.query.CheckAttendanceRecordExists(c.Request().Context(), database.CheckAttendanceRecordExistsParams{
			UserID: int64(req.User.ID),
			State:  "qr-in",
			Time: sql.NullTime{
				Time:  dateOnly,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: req.User.BookType,
				Valid:  true,
			},
		})
		if err != nil {
			log.Printf("[ERROR 179: OpenDoor ] >> | error database | %v", err)
			if exitErr := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); exitErr != nil {
				log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", exitErr)
			}
			log.Printf("[ERROR] >> | error database | %v", err)
		}

		if isAlreadyIn {
			log.Printf("[ERROR 185: OpenDoor] >> user already in")

			isAlreadyOut, err := H.query.CheckAttendanceRecordExists(c.Request().Context(), database.CheckAttendanceRecordExistsParams{
				UserID: int64(req.User.ID),
				State:  "qr-out",
				Time: sql.NullTime{
					Time:  dateOnly,
					Valid: true,
				},
				Booktype: sql.NullString{
					String: req.User.BookType,
					Valid:  true,
				},
			})
			if err != nil {
				log.Printf("[ERROR] >> | error database | %v", err)
			}
			if isAlreadyOut {
				if exitErr := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); exitErr != nil {
					log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", exitErr)
				}
				return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.USERALREADYOUT, "user already out"))
			}
			if exitErr := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); exitErr != nil {
				log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", exitErr)
			}
			return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.USERALREADYIN, "user already in"))
		}

	} else if qrData.ConnType == "qr-out" {
		// For checkout: First check if user has checked in today
		hasCheckedIn, err := H.query.CheckAttendanceRecordExists(c.Request().Context(), database.CheckAttendanceRecordExistsParams{
			UserID: int64(req.User.ID),
			State:  "qr-in", // Check for check-in record
			Time: sql.NullTime{
				Time:  dateOnly,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: req.User.BookType,
				Valid:  true,
			},
		})
		if err != nil {
			log.Printf("[ERROR] >> | error database | %v", err)
		}

		// If user hasn't checked in, reject checkout
		if !hasCheckedIn {
			if exitErr := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); exitErr != nil {
				log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", exitErr)
			}
			return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.USERNOTCHECKEDIN, "user must check in before checking out"))
		}

		// Then check if user has already checked out today
		hasCheckedOut, err := H.query.CheckAttendanceRecordExists(c.Request().Context(), database.CheckAttendanceRecordExistsParams{
			UserID: int64(req.User.ID),
			State:  "qr-out", // Check for check-out record
			Time: sql.NullTime{
				Time:  dateOnly,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: req.User.BookType,
				Valid:  true,
			},
		})
		if err != nil {
			log.Printf("[ERROR] >> | error database | %v", err)
		}

		// If user has already checked out, reject duplicate checkout
		if hasCheckedOut {
			if exitErr := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); exitErr != nil {
				log.Printf("[ERROR] >> EmergenceyExitFunc failed: %v", exitErr)
			}
			return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.USERALREADYOUT, "user already out"))
		}
	}

	// // get qr client
	// qrClient, exits := H.hub.GetClient(qrData.ClientId)
	// if !exits {
	// 	return c.JSON(http.StatusBadRequest, "client not found")
	// }
	//
	// // check if the qr client is in use
	// if qrClient.GetProcessing() {
	// 	return c.JSON(http.StatusBadRequest, "QR Client alreadt in use")
	// }
	//
	// qrClient.SetProcessing(true)
	// qrClient.SetStatus(code.QRPROCESSING)

	if doorData.CheckIsOpen(client) {
		for _, cli := range doorData.GetQRDevices() {
			err := H.hub.SendToClient(cli.Id, types.MessageType{
				MsgType: "DoorStillOpen",
				Data:    []byte(`{"state": "dooropenerror"}`),
			})
			if err != nil {
				log.Printf("[ERROR - OpenDoor] Failed to send DoorStillOpen message to client %s: %v", cli.Id, err)
			}
		}
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.DoorOpen, "door open already"))
	}

	// TODO: before msg send, need to check whether the user already in, and users credential
	// send msg to WS
	message := types.MessageType{
		MsgType: "unlock",
		Data:    []byte(`{"isUnlock": true}`),
	}

	err = client.SendMsg(message)
	if err != nil {
		for _, cli := range doorData.GetQRDevices() {
			sendErr := H.hub.SendToClient(cli.Id, types.MessageType{
				MsgType: "processError",
				Data:    []byte(`{"isProcessing": false}`),
			})
			if sendErr != nil {
				log.Printf("[ERROR - OpenDoor] Failed to send processError message to client %s: %v", cli.Id, sendErr)
			}
		}
		log.Printf("[ERROR - OpenDoor] Failed to send unlock message: %v", err)
		if exitErr := EmergenceyExitFunc(H, client, doorData, req.QRcode, qrData.ClientId); exitErr != nil {
			log.Printf("[ERROR - OpenDoor] EmergenceyExitFunc failed: %v", exitErr)
		}
		return c.JSON(http.StatusInternalServerError, "failed to send unlock message")
	}

	// JWT tokens are stateless, no need to delete from store

	// for _, cli := range doorData.GetQRDevices() {
	// 	// TODO: there should be a error check here
	// 	H.hub.SendToClient(cli.Id, types.MessageType{
	// 		MsgType: "unlock-attempt-success",
	// 		Data:    []byte(`{"isProcessing": false}`),
	// 	})
	// }
	//
	// qrClient.SendMsg(types.MessageType{
	// 	MsgType: "generate-new",
	// 	Data:    []byte(`{"isProcessing": false}`),
	// })
	//
	// client.SendMsg(types.MessageType{
	// 	MsgType: "unlock-attempt-success",
	// 	Data:    []byte(`{"isProcessing": false}`),
	// })

	devieceIntId, err := strconv.ParseInt(qrData.ReqDeviceId, 10, 64)
	if err != nil {
		log.Printf("[ERROR] >> %v", err)
	}

	gymData, err := types.GymDataToNullString(&req.User.GymData)
	if err != nil {
		log.Printf("[ERROR] >> %v", err)
	}

	serviceData, err := types.ServiceDataToNullString(req.User.ServiceData)
	if err != nil {
		log.Printf("[ERROR] >> %v", err)
	}

	err = H.query.CreateAttendee(c.Request().Context(), database.CreateAttendeeParams{
		AttendeeID: userID,
		State:      qrData.ConnType,
		BranchID:   int64(qrData.BranchId),
		DeviceID: sql.NullInt64{
			Int64: devieceIntId,
			Valid: true,
		},
		Subscription: sql.NullString{
			Valid: false,
		},
		Booktype: sql.NullString{
			String: req.User.BookType,
			Valid:  true,
		},
		ServiceData: serviceData,
		GymData:     gymData,
	})
	if err != nil {
		log.Printf("[ERROR] || >> %v", err)
	}

	// Send success messages to all QR devices
	for _, cli := range doorData.GetQRDevices() {
		err := H.hub.SendToClient(cli.Id, types.MessageType{
			MsgType: "unlock-success",
			Data:    []byte(`{"status": "success", "message": "Door opened successfully"}`),
		})
		if err != nil {
			log.Printf("[ERROR - OpenDoor] Failed to send success message to QR client %s: %v", cli.Id, err)
		}
	}

	// Send generate-new message to the requesting QR client to refresh QR code
	err = H.hub.SendToClient(qrData.ClientId, types.MessageType{
		MsgType: "generate-new",
		Data:    []byte(`{"status": "success", "refresh": true}`),
	})
	if err != nil {
		log.Printf("[ERROR - OpenDoor] Failed to send generate-new message to requesting client %s: %v", qrData.ClientId, err)
	}

	log.Printf("[OpenDoor] Door opened successfully for user %d via device %s", req.User.ID, qrData.ReqDeviceId)

	// JWT tokens are stateless, no need to delete from store
	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    true,
		"message": "Door opened successfully",
		"user":    req.User.Name,
		"device":  qrData.ReqDeviceId,
	})
}

func (H *HttpRoutes) GenQRDetails(c echo.Context) error {
	var req types.QRCodeHTTPReq
	err := c.Bind(&req)
	if err != nil {
		return c.JSON(http.StatusBadRequest, "request is not valid")
	}

	client, exits := H.hub.GetClient(req.ClientId)
	if !exits {
		return c.JSON(http.StatusBadRequest, "client not found")
	}

	doorData, ok := client.Data.(*ws.DoorLockType)
	if !ok {
		return c.JSON(http.StatusBadRequest, "client data is not valid")
	}

	if doorData.CheckIsOpen(client) {
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.DoorOpen, "door open already"))
	}

	return c.JSON(http.StatusOK, nil)
}

func (H *HttpRoutes) GetConnectedClients(c echo.Context) error {
	clients := H.hub.GetClientsData()
	if len(clients) == 0 {
		return c.JSON(http.StatusNotFound, "no clients found")
	}

	return c.JSON(http.StatusOK, clients)
}

func (H *HttpRoutes) ScanQRCode(c echo.Context) error {
	H.tstore.DrawTUITokenStoreData()

	var req types.DoorLockAccessReq
	err := c.Bind(&req)
	if err != nil {
		return c.JSON(http.StatusBadRequest, "request is not valid")
	}
	log.Printf("[SCAN] >> %v", req)

	// Validate JWT token and extract QR code info
	claim, err := utils.ValidateToken(req.QRcode)
	if err != nil {
		log.Printf("[ERROR] >> %v", "qr invalid")
		return c.JSON(http.StatusUnauthorized, "invalid qr code")
	}
	qrData := claim.QRInfo

	return c.JSON(http.StatusOK, qrData)
}

func (H *HttpRoutes) GenerateApi(c echo.Context) error {
	// Generate a secure random API key (32 bytes = 256 bits of entropy)
	keyBytes := make([]byte, 32)
	_, err := rand.Read(keyBytes)
	if err != nil {
		return err
	}

	// Encode the random bytes as base64 to make it usable as an API key
	// This is what will be provided to the client
	apiKey := base64.URLEncoding.EncodeToString(keyBytes)
	hashedKey := utils.HashAPIKey(apiKey)

	return c.JSON(http.StatusOK, map[string]string{
		"apiKey":    apiKey,
		"hashedKey": hashedKey,
	})
}

func (H *HttpRoutes) ActiveDeviceCountAll(c echo.Context) error {
	actClient := H.hub.GetDoorClients()
	count := len(actClient)
	return c.JSON(http.StatusOK, map[string]int{
		"activeDeviceCount": count,
	})
}

func (H *HttpRoutes) GetTokenStore(c echo.Context) error {
	key := c.Param("key")
	if key == "" {
		return c.JSON(http.StatusBadRequest, "tt -> mm -> kel-? I k U A T T H")
	}
	if key == "71a69863-2ab5-452d-9730-a1d717b814f1" {
		return c.JSON(http.StatusBadRequest, "tt -> mm -> kel-? I k U A T T H")
	}

	H.tstore.DrawTUITokenStoreData()
	return c.JSON(http.StatusOK, "secure me opened")
}

// func (H *HttpRoutes) DisconnectDoorClinet(c echo.Context) error {
// var req types.DisconnectDoorClinetType
// err := c.Bind(&req)
// if err != nil {
// 	return c.JSON(http.StatusBadRequest, "request is not valid")
// }
//
// client, exits := H.hub.GetClient(req.ClientId)
// if !exits {
// 	return c.JSON(http.StatusBadRequest, "client not found")
// }
//
// 	client.SafeDisconnect()
//
// 	return c.JSON(http.StatusOK, types.NewAppResponseMsg(code.SucessFullyUnlocked, "unlocked successfully"))
// }

func (H *HttpRoutes) EmergenceyOpenDoor(c echo.Context) error {
	var req types.EmergencyDoorOpen
	err := c.Bind(&req)
	if err != nil {
		log.Printf("[ERROR - EmergencyOpenDoor] Request binding failed: %v", err)
		return c.JSON(http.StatusBadRequest, "request is not valid")
	}

	log.Printf("[EmergencyOpenDoor] Request: %v", req)

	// Check password against configured emergency password
	if req.Password != H.cfg.EmergencyPassword {
		log.Printf("[ERROR - EmergencyOpenDoor] Invalid emergency password attempt")
		return c.JSON(http.StatusUnauthorized, "invalid password")
	}

	// check if the door is lock if not let the user know
	client, exits := H.hub.GetClient(req.DeviceID)
	if !exits {
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "client not found"))
	}

	// check if the client is in use
	if client.GetProcessing() {
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINUSE, "client is already in use"))
	}

	log.Printf("[client data] >> %v", client)

	doorData, ok := client.Data.(*ws.DoorLockType)
	if !ok {
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "client data is not valid"))
	}
	log.Printf("[doordata data] >> %v", doorData)

	// set the QR client to processing
	for _, cli := range doorData.GetQRDevices() {
		// TODO: there should be a error check here
		err := H.hub.SendToClient(cli.Id, types.MessageType{
			MsgType: "processing",
		})
		if err != nil {
			client.SetProcessing(false)
			log.Printf("[ERROR] >> %v", err)
			return c.JSON(http.StatusInternalServerError, "failed to send processing message")
		}
	}

	// -------------------------------------------------safe area start-------------------------------------------------

	client.SetProcessing(true)
	client.SetStatus(code.QRPROCESSING)

	// if !slices.Contains(doorData.QRDevices, qrData.ClientId) {
	// 	return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "client is not a qr device"))
	// }

	if doorData.CheckIsOpen(client) {
		for _, cli := range doorData.GetQRDevices() {
			// TODO: there should be a error check here
			H.hub.SendToClient(cli.Id, types.MessageType{
				MsgType: "DoorStillOpen",
				Data:    []byte(`{"state": "dooropenerror"}`),
			})
		}
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.DoorOpen, "door opne already"))
	}

	// TODO: before msg send, need to check whether the user already in, and users credential
	// send msg to WS
	message := types.MessageType{
		MsgType: "unlock",
		Data:    []byte(`{"isUnlock": true}`),
	}

	err = client.SendMsg(message)
	if err != nil {
		for _, cli := range doorData.GetQRDevices() {
			// TODO: there should be a error check here
			H.hub.SendToClient(cli.Id, types.MessageType{
				MsgType: "processError",
				Data:    []byte(`{"isProcessing": false}`),
			})
		}
		log.Printf("[ERROR] >> %v", err)
		return c.JSON(http.StatusInternalServerError, "failed to send unlock message")
	}

	return c.JSON(http.StatusOK, types.NewAppResponseMsg(code.SucessFullyUnlocked, "unlocked successfully"))
}
