package http

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
)

// func (H *HttpRoutes) InsertUser(c echo.Context) error {
// 	var req CreateDoorLockUserParams
// 	err := c.Bind(&req)
// 	if err != nil {
// 		return c.JSON(http.StatusBadRequest, "request is not valid")
// 	}
// 	params, err := req.convertToDB()
// 	if err != nil {
// 		return c.JSON(http.StatusBadRequest, "request is not valid")
// 	}
// 	err = H.query.InsertDoorLockUser(c.Request().Context(), *params)
// 	if err != nil {
// 		return c.JSON(500, err.Error())
// 	}
//
// 	return c.JSON(http.StatusOK, "user inserted successfully")
// }

func (H *HttpRoutes) GetUser(c echo.Context) error {
	branch_id, err := strconv.ParseInt(c.Param("branch_id"), 10, 64)
	if err != nil {
		return c.JSON(500, err.Error())
	}
	users, err := H.query.GetDoorLockUser(c.Request().Context(), branch_id)
	if err != nil {
		return c.JSON(500, err.Error())
	}
	return c.JSON(http.StatusOK, users)
}

func (H *HttpRoutes) CheckUserExistace(c echo.Context) error {
	user_id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return c.JSON(500, err.Error())
	}
	user, err := H.query.CheckUserExists(c.Request().Context(), user_id)
	if err != nil {
		return c.JSON(500, err.Error())
	}
	return c.JSON(http.StatusOK, user)
}
