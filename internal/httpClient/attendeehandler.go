package http

import (
	"database/sql"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/labstack/echo/v4"
)

func (H *HttpRoutes) CreateAttendee(c echo.Context) error {
	var reqparams database.CreateAttendeeParams
	err := c.Bind(&reqparams)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	result := H.query.CreateAttendee(c.Request().Context(), reqparams)
	if result != nil {
		return c.JSON(http.StatusInternalServerError, result.Error())
	}
	return c.JSON(http.StatusOK, "attendee created successfully")
}

func (H *HttpRoutes) GetAllAttendee(c echo.Context) error {
	attendees, err := H.query.GetAllAttendee(c.Request().Context())
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, attendees)
}

func (H *HttpRoutes) GetAttendeeByID(c echo.Context) error {
	attendeeID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	attendee, err := H.query.GetAttendeeByAttendeeID(c.Request().Context(), attendeeID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, attendee)
}

func (H *HttpRoutes) GetAttendeeByIDState(c echo.Context) error {
	var reqparams database.GetAttendeeByAttendeeIDAndStateParams
	err := c.Bind(&reqparams)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	attendee, err := H.query.GetAttendeeByAttendeeIDAndState(c.Request().Context(), reqparams)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, attendee)
}

// CountAttendeesRequest is used to bind the incoming JSON for the count endpoint.
type CountAttendeesRequest struct {
	BranchID int64 `json:"branch_id" validate:"required"`
	// The state filter (if empty, all states are counted)
	State string `json:"state"`
	// Time must be provided in YYYY-MM-DD format
	Time string `json:"time" validate:"required"`
}

// GetAttendeesRequest is used to bind the incoming JSON for the retrieval endpoint.
type GetAttendeesRequest struct {
	BranchID int64 `json:"branch_id" validate:"required"`
	DeviceID int64 `json:"device_id" validate:"required"`
	// The state filter (if empty, returns all)
	State string `json:"state"`
	// Time must be provided in YYYY-MM-DD format (to filter the specific day)
	Time string `json:"time" validate:"required"`
	// SortOrder should be "asc" or "desc" for sorting on the date
	SortOrder string `json:"sort_order"`
	// Pagination fields
	Limit  int32 `json:"limit" validate:"required,min=1"`
	Offset int32 `json:"offset" validate:"required,min=0"`
}

func (H *HttpRoutes) GetAttendeeCounts(c echo.Context) error {
	var req CountAttendeesRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{
			"error":   "invalid request payload",
			"details": err.Error(),
		})
	}

	// Parse the date provided in the request (expected format "2006-01-02")
	parsedTime, err := time.Parse("2006-01-02", req.Time)
	if err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{
			"error": "invalid date format, expected YYYY-MM-DD",
		})
	}

	// Prepare the params: Notice that Column2 is used to handle the empty state check.
	params := database.CountAttendeesParams{
		BranchID: req.BranchID,
		Column2:  req.State,
		State:    req.State,
		Time:     sql.NullTime{Time: parsedTime, Valid: true},
	}

	count, err := H.query.CountAttendees(c.Request().Context(), params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, echo.Map{
			"error":   "failed to count attendees",
			"details": err.Error(),
		})
	}

	return c.JSON(http.StatusOK, echo.Map{
		"count": count,
	})
}

// Handler to get attendees with pagination
func (H *HttpRoutes) GetAttendeeRecAll(c echo.Context) error {
	var req GetAttendeesRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{
			"error":   "invalid request payload",
			"details": err.Error(),
		})
	}

	// Parse the provided date (format "2006-01-02")
	parsedTime, err := time.Parse("2006-01-02", req.Time)
	if err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{
			"error": "invalid date format, expected YYYY-MM-DD",
		})
	}

	// Set up the sort order parameters based on the SortOrder field
	var orderAsc, orderDesc interface{}
	switch strings.ToLower(req.SortOrder) {
	case "asc":
		orderAsc = "asc"
		orderDesc = ""
	case "desc":
		orderAsc = ""
		orderDesc = "desc"
	default:
		// Default: no specific order on date
		orderAsc = ""
		orderDesc = ""
	}

	params := database.GetAttendeesByBranchWithPaginationParams{
		BranchID: req.BranchID,
		DeviceID: sql.NullInt64{
			Int64: req.DeviceID,
			Valid: true,
		},
		Column3: req.State,
		State:   req.State,
		Time:    sql.NullTime{Time: parsedTime, Valid: true},
		Column6: orderAsc,
		Column7: orderDesc,
		Limit:   req.Limit,
		Offset:  req.Offset,
	}

	attendees, err := H.query.GetAttendeesByBranchWithPagination(c.Request().Context(), params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, echo.Map{
			"error":   "failed to get attendees",
			"details": err.Error(),
		})
	}

	// Convert database rows to enhanced response format
	responseData := make([]*types.AttendeeResponse, 0, len(attendees))
	for i := range attendees {
		respItem, err := types.ConvertToResponse(&attendees[i])
		if err != nil {
			return c.JSON(http.StatusInternalServerError, echo.Map{
				"error":   "failed to process attendee data",
				"details": err.Error(),
			})
		}
		responseData = append(responseData, respItem)
	}

	return c.JSON(http.StatusOK, echo.Map{
		"success": true,
		"count":   len(responseData),
		"data":    responseData,
	})
}
