package types

import "encoding/json"

type WSConnectReq struct {
	ConnType string `json:"connType"` // qr-in | qr-out | door-lock | monitor
	Id       int    `json:"id"`
	ReqId    string `json:"reqId"`
	APIKey   string `json:"apiKey"`
	BranchId int    `json:"branchId"`
}

type MessageType struct {
	MsgType string          `json:"msgType"`
	Data    json.RawMessage `json:"data"`
}

type QRConnectReq struct {
	ConnType    string `json:"connType"` // qr-in | qr-out
	ReqDeviceId string `json:"reqDeviceId"`
	BranchId    int    `json:"branchId"`
}

type NotifyClientReq struct {
	DeviceId []int `json:"deviceId"`
}

type NotifyT struct {
	Type string      `json:"type"` // notify
	Msg  string      `json:"msg"`  // notify
	Data interface{} `json:"data"` // data to send
}
