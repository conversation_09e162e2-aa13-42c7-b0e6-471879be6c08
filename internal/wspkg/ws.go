package wspkg

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/config"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/labstack/echo/v4"
)

// ErrorResponse represents a standardized error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// NewErrorResponse creates a standardized error response
func NewErrorResponse(code int, message string) ErrorResponse {
	return ErrorResponse{
		Error:   "error",
		Code:    code,
		Message: message,
	}
}

type WebSocketCtx struct {
	manger      *Manager
	db          *database.Queries
	tstore      *utils.TokenStore
	ClientState *ClientState
	Pubsub      *PubSubManager
	Config      *config.Config
}

func NewWebSocketCtx(manger *Manager, db *database.Queries, tstore *utils.TokenStore, pubsub *PubSubManager, config *config.Config) *WebSocketCtx {
	return &WebSocketCtx{
		manger:      manger,
		db:          db,
		tstore:      tstore,
		ClientState: NewClientState(),
		Pubsub:      pubsub,
		Config:      config,
	}
}

// GetManager returns the WebSocket manager
func (W *WebSocketCtx) GetManager() *Manager {
	return W.manger
}

func (W *WebSocketCtx) AuthenticateClient(c echo.Context) error {
	// ... existing authentication code ...
	var conntypereq types.WSConnectReq
	err := c.Bind(&conntypereq)
	if err != nil {
		W.manger.logger.Error(
			"Bind Error @ AuthenticateClient",
			Field{Key: "Error", Value: err},
			Field{Key: "Request", Value: c.Request().Body},
		)
		return c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid JSON request"))
	}
	checkData := database.ValidateDeviceDetailsParams{
		ID:       int64(conntypereq.Id),
		ApiKey:   utils.HashAPIKey(conntypereq.APIKey),
		BranchID: int64(conntypereq.BranchId),
	}
	W.manger.logger.Debug(
		"Checking Device Details @ AuthenticateClient",
		Field{Key: "CheckData", Value: checkData},
		Field{Key: "Request", Value: conntypereq},
	)
	isValid, err := W.db.ValidateDeviceDetails(c.Request().Context(), checkData)
	if err != nil {
		W.manger.logger.Error(
			"Database Error @ AuthenticateClient",
			Field{Key: "Error", Value: err},
			Field{Key: "Request", Value: conntypereq},
		)
		return c.JSON(http.StatusUnauthorized, NewErrorResponse(http.StatusUnauthorized, "API key validation failed"))
	}
	if len(isValid) <= 0 {
		W.manger.logger.Error(
			"Invalid API Key @ AuthenticateClient",
			Field{
				Key:   "Request",
				Value: conntypereq,
			},
		)
		return c.JSON(http.StatusUnauthorized, NewErrorResponse(http.StatusUnauthorized, "API key not valid with credentials"))
	}

	// checkApi, err := utils.ValidateAPIKey(W.query, int64(conntypereq.Id), conntypereq.APIKey)
	// if err != nil {
	// 	return c.JSON(http.StatusUnauthorized, "Api key error")
	// }
	// if !checkApi {
	// 	return c.JSON(http.StatusUnauthorized, "Api key not valid")
	// }

	// Generate token
	token := utils.GenerateSecureToken()

	// Store token with 2-minute expiration
	W.tstore.StoreToken(
		token,
		strconv.Itoa(conntypereq.Id),
		conntypereq.ConnType,
		conntypereq.BranchId,
		"",
		conntypereq.APIKey,
		time.Now().Add(5*time.Minute),
	)

	return c.JSON(http.StatusOK, map[string]string{"token": token})
}

func (W *WebSocketCtx) DoorClientWS(c echo.Context) error {
	// Create context with timeout for the entire operation
	ctx, cancel := context.WithTimeout(c.Request().Context(), 30*time.Second)
	defer cancel()

	token := c.QueryParam("token")
	tokenData, isValid := W.tstore.ValidateToken(token)
	if !isValid {
		W.manger.logger.Error(
			"Token Validation Error @ DoorClientWS",
			Field{Key: "Token", Value: token},
			Field{Key: "Validate", Value: isValid},
		)
		return c.JSON(http.StatusUnauthorized, NewErrorResponse(http.StatusUnauthorized, "Invalid token"))
	}

	W.tstore.RemoveToken(token)

	if tokenData.ConnType != "door-lock" {
		W.manger.logger.Error(
			"Connection Type Error @ DoorClientWS",
			Field{Key: "TokenData", Value: tokenData},
		)
		return c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid connection type"))
	}

	deviceId, err := strconv.ParseInt(tokenData.ClientId, 10, 64)
	if err != nil {
		W.manger.logger.Error(
			"invalid Client ID @ DoorClientWS",
			Field{Key: "ClientId", Value: tokenData.ClientId},
		)
		return c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid client ID"))
	}

	deviceData, err := W.db.GetDeviceData(ctx, deviceId)
	if err != nil {
		W.manger.logger.Error(
			"Error fetching device data @ DoorClientWS",
			Field{Key: "DeviceId", Value: deviceId},
			Field{Key: "Error", Value: err},
		)
		return c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to fetch device data"))
	}

	err = W.db.UpdateDoorOnlineStatus(c.Request().Context(), database.UpdateDoorOnlineStatusParams{
		IsOnline: true,
		ID:       deviceId,
	})
	if err != nil {
		return c.String(http.StatusInternalServerError, "failed to update online status")
	}

	conn, err := W.manger.upgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		W.manger.logger.Error(
			"websocket upgrade error @ DoorClientWS",
			Field{Key: "Error", Value: err},
			Field{Key: "DeviceId", Value: deviceId},
		)
		return c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to upgrade connection"))
	}

	connClient := NewClient(conn)
	connClient.SetOnline(true)

	// Set close handler with proper cleanup
	connClient.SetCloseHandler(func(client *Client) error {
		// Remove from client state
		W.ClientState.RemoveDoorClient(client.Id)

		// Log disconnection
		W.manger.logger.Info(
			"Door client disconnected",
			Field{Key: "ClientId", Value: client.Id},
			Field{Key: "DeviceId", Value: deviceId},
		)
		return nil
	})

	// Set error handler with structured logging
	connClient.SetErrHandler(func(err error) {
		W.manger.logger.Error(
			"Error in door client",
			Field{Key: "ClientId", Value: connClient.Id},
			Field{Key: "DeviceId", Value: deviceId},
			Field{Key: "Error", Value: err},
		)
	})

	W.ClientState.CreateDoorClient(connClient.Id, deviceData.ID, deviceData.DeviceName, int(deviceData.BranchID))

	isAdd := W.manger.AddExistingClient(connClient)
	if !isAdd {
		// Comprehensive cleanup on failure
		connClient.Close()
		W.ClientState.RemoveDoorClient(connClient.Id)
		W.manger.logger.Error(
			"error adding existing client @ DoorClientWS",
			Field{Key: "ClientId", Value: connClient.Id},
			Field{Key: "DeviceId", Value: deviceId},
		)
		return c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to add client - server at capacity"))
	}

	// Start pump goroutines
	go W.manger.ReadPumpW(connClient)
	go W.manger.WritePumpW(connClient)

	// Subscribe to Redis channels with error handling
	channels := []string{"all", fmt.Sprintf("device-%d", deviceData.ID)}
	err = W.Pubsub.Subscribe(channels, func(channel string, msg *Message) {
		// Check if client is still connected before sending
		if connClient.IsConnected() {
			connClient.Send(msg)
		}
	})
	if err != nil {
		// Comprehensive cleanup on subscription failure
		connClient.Close()
		W.manger.RemoveClient(connClient.Id)
		W.ClientState.RemoveDoorClient(connClient.Id)
		W.manger.logger.Error(
			"Error subscribing to pubsub @ DoorClientWS",
			Field{Key: "Error", Value: err},
			Field{Key: "Channels", Value: channels},
			Field{Key: "DeviceId", Value: deviceId},
		)
		return c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to subscribe to message channels"))
	}

	W.manger.logger.Info(
		"Door client connected successfully",
		Field{Key: "ClientId", Value: connClient.Id},
		Field{Key: "DeviceId", Value: deviceId},
		Field{Key: "DeviceName", Value: deviceData.DeviceName},
	)
	msg, err := NewMessageFromJSON("notify", types.NotifyT{
		Type: "device-connected",
		Msg:  fmt.Sprintf("door lock connected [device No : %d]", deviceData.ID),
	})
	W.Pubsub.Publish(fmt.Sprintf("device-%d-notify", deviceData.ID), msg)
	if err != nil {
		W.manger.logger.Error("Error @ pubsub door ws connceting")
	}

	return nil
}

func (W *WebSocketCtx) AuthenticateQRClient(c echo.Context) error {
	var conntypereq types.QRConnectReq
	err := c.Bind(&conntypereq)
	if err != nil {
		W.manger.logger.Error(
			"websocket bind error @ AuthenticateQRClient",
			Field{Key: "Error", Value: err},
		)
		return c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid JSON request"))
	}

	reqDeviceId, err := strconv.ParseInt(conntypereq.ReqDeviceId, 10, 64)
	if err != nil {
		W.manger.logger.Error(
			"Error parsing ReqDeviceId @ AuthenticateQRClient",
			Field{Key: "ReqDeviceId", Value: conntypereq.ReqDeviceId},
		)
		return c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid device ID"))
	}

	// ATOMIC CHECK: Single lock acquisition for both validation checks
	var doorExists bool
	var slotAvailable bool

	W.ClientState.mut.RLock()
	for _, door := range W.ClientState.DoorLocks {
		if door.DoorId == reqDeviceId {
			doorExists = true
			// Use doorClient's mutex to safely read QR associations
			door.mu.RLock()
			switch conntypereq.ConnType {
			case "qr-in":
				slotAvailable = (door.qrIn == nil)
			case "qr-out":
				slotAvailable = (door.qrOut == nil)
			default:
				// Invalid connection type
				slotAvailable = false
			}
			door.mu.RUnlock()
			break
		}
	}
	W.ClientState.mut.RUnlock()

	if !doorExists {
		return c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "device not found"))
	}

	if !slotAvailable {
		return c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "device already connected with this connection type"))
	}

	// Generate token
	token := utils.GenerateSecureToken()

	// Store token with 2-minute expiration
	W.tstore.StoreToken(
		token,
		"",
		conntypereq.ConnType,
		conntypereq.BranchId,
		conntypereq.ReqDeviceId,
		"",
		time.Now().Add(2*time.Minute),
	)

	return c.JSON(http.StatusOK, map[string]string{
		"token": token,
	})
}

func (W *WebSocketCtx) QRClientWS(c echo.Context) error {
	token := c.QueryParam("token")
	tokenInfo, isValid := W.tstore.ValidateToken(token)
	if !isValid {
		return c.String(http.StatusUnauthorized, "token access issue")
	}

	W.tstore.RemoveToken(token)
	reqDeviceId, err := strconv.ParseInt(tokenInfo.ReqId, 10, 64)
	if err != nil {
		W.manger.logger.Error(
			"error parsing ReqId @ QRClientConnectingHandler",
			Field{Key: "ReqId", Value: tokenInfo.ReqId},
		)
		return c.String(http.StatusBadRequest, "invalid device ID")
	}

	checkDevice := W.ClientState.CheckDoorExist(reqDeviceId)
	if !checkDevice {
		W.manger.logger.Error(
			"error getting door client @ QRClientConnectingHandler",
		)
		return c.String(http.StatusBadRequest, "device not found")
	}

	// Fix 1: Add missing return statement after WebSocket upgrade error
	conn, err := W.manger.upgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		W.manger.logger.Error(
			"Error upgrading connection @ QRClientConnectingHandler",
			Field{Key: "Error", Value: err},
		)
		return c.String(http.StatusInternalServerError, "Failed to upgrade connection")
	}

	// Fix 2: Remove redundant client addition - use AddExistingClient approach only
	client := NewClient(conn)
	client.SetOnline(true)
	client.SetCloseHandler(func(c *Client) error {
		W.ClientState.RemoveQRClient(client.Id)
		return nil
	})
	client.SetErrHandler(func(err error) {
		W.manger.logger.Error(
			"Error in client connection @ QRClientConnectingHandler",
			Field{Key: "Error", Value: err},
		)
	})

	// Fix 3: Add complete client state cleanup on failure
	isAdd := W.manger.AddExistingClient(client)
	if !isAdd {
		conn.Close()                            // Clean up the connection
		W.ClientState.RemoveQRClient(client.Id) // Clean up client state
		W.manger.logger.Error(
			"error adding existing client @ QRClientConnectingHandler",
		)
		return c.String(http.StatusInternalServerError, "Failed to add existing client")
	}

	// Create QR client state
	switch tokenInfo.ConnType {
	case "qr-in":
		W.ClientState.CreateQRClientWithDoorCheckin(client.Id, "qr-in", tokenInfo.BranchId, reqDeviceId)
	case "qr-out":
		W.ClientState.CreateQRClientWithDoorCheckin(client.Id, "qr-out", tokenInfo.BranchId, reqDeviceId)
	default:
		// Fix 4: Add proper cleanup for invalid connection type
		conn.Close()
		W.manger.RemoveClient(client.Id)
		W.ClientState.RemoveQRClient(client.Id)
		W.manger.logger.Error(
			"invalid connection type @ QRClientConnectingHandler",
			Field{Key: "ConnType", Value: tokenInfo.ConnType},
		)
		return c.String(http.StatusBadRequest, "invalid connection type")
	}

	go W.manger.ReadPumpW(client)
	go W.manger.WritePumpW(client)

	err = W.Pubsub.Subscribe(
		[]string{"all", fmt.Sprintf("device-%s", tokenInfo.ReqId)},
		func(channel string, msg *Message) {
			client.Send(msg)
		},
	)
	if err != nil {
		client.Close() // Clean up the connection
		W.manger.logger.Error(
			"Error subscribing to pubsub @ DoorClientWS",
			Field{Key: "Error", Value: err},
		)
		return c.String(http.StatusInternalServerError, "Failed to subscribe to pubsub")
	}
	msg, err := NewMessageFromJSON("notify", types.NotifyT{
		Type: "device-connected",
		Msg:  fmt.Sprintf("qr connected [device No : %s]", tokenInfo.ReqId),
	})
	W.Pubsub.Publish(fmt.Sprintf("device-%s-notify", tokenInfo.ReqId), msg)
	if err != nil {
		W.manger.logger.Error("Error @ pubsub door ws connceting")
	}

	return nil
}

func (W *WebSocketCtx) NotifiyWS(c echo.Context) error {
	idsParamas := c.QueryParams()["id"]
	if len(idsParamas) == 0 {
		W.manger.logger.Error("No device IDs provided @ NotifiyWS")
		return c.String(http.StatusBadRequest, "at least one device ID is required")
	}

	var deviceIds []string
	for _, id := range idsParamas {
		idInt, err := strconv.Atoi(id)
		if err != nil {
			W.manger.logger.Error(
				"Error parsing device ID @ NotifiyWS",
				Field{Key: "DeviceId", Value: id},
			)
			return c.String(http.StatusBadRequest, "invalid device ID")
		}
		deviceIds = append(deviceIds, fmt.Sprintf("device-%d-notify", idInt))
	}

	conn, err := W.manger.upgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		W.manger.logger.Error(
			"websocket upgrade error @ NotifiyWS",
			Field{Key: "Error", Value: err},
		)
		return c.String(http.StatusInternalServerError, "Failed to upgrade connection")
	}

	// Create client with proper setup
	client := NewClient(conn)
	client.SetOnline(true)

	// Set close handler with proper cleanup
	client.SetCloseHandler(func(client *Client) error {
		W.manger.logger.Info(
			"Notify client disconnected",
			Field{Key: "ClientId", Value: client.Id},
			Field{Key: "DeviceIds", Value: deviceIds},
		)
		return nil
	})

	// Set error handler with structured logging
	client.SetErrHandler(func(err error) {
		W.manger.logger.Error(
			"Error in notify client",
			Field{Key: "ClientId", Value: client.Id},
			Field{Key: "DeviceIds", Value: deviceIds},
			Field{Key: "Error", Value: err},
		)
	})

	// Add client to manager
	isAdd := W.manger.AddExistingClient(client)
	if !isAdd {
		// Comprehensive cleanup on failure
		client.Close()
		W.manger.logger.Error(
			"error adding existing client @ NotifiyWS",
			Field{Key: "ClientId", Value: client.Id},
			Field{Key: "DeviceIds", Value: deviceIds},
		)
		return c.String(http.StatusInternalServerError, "Failed to add client - server at capacity")
	}

	// Start pump goroutines for message handling
	go W.manger.ReadPumpW(client)
	go W.manger.WritePumpW(client)

	// Subscribe to Redis channels with improved handler
	err = W.Pubsub.Subscribe(
		deviceIds,
		func(channel string, msg *Message) {
			// Check if client is still connected before sending
			if client.IsConnected() {
				W.manger.logger.Debug(
					"Received message for notify client",
					Field{Key: "ClientId", Value: client.Id},
					Field{Key: "Channel", Value: channel},
					Field{Key: "Event", Value: msg.GetEvent()},
				)

				// Send all messages that start with "notify" or are general notifications
				if strings.HasPrefix(msg.GetEvent(), "notify") || msg.GetEvent() == "notification" {
					client.Send(msg)
					W.manger.logger.Debug(
						"Message sent to notify client",
						Field{Key: "ClientId", Value: client.Id},
						Field{Key: "Event", Value: msg.GetEvent()},
					)
				}
			}
		},
	)
	if err != nil {
		// Comprehensive cleanup on subscription failure
		client.Close()
		W.manger.RemoveClient(client.Id)
		W.manger.logger.Error(
			"Error subscribing to pubsub @ NotifiyWS",
			Field{Key: "Error", Value: err},
			Field{Key: "DeviceIds", Value: deviceIds},
			Field{Key: "ClientId", Value: client.Id},
		)
		return c.String(http.StatusInternalServerError, "Failed to subscribe to message channels")
	}

	W.manger.logger.Info(
		"Notify client connected successfully",
		Field{Key: "ClientId", Value: client.Id},
		Field{Key: "DeviceIds", Value: deviceIds},
		Field{Key: "SubscribedChannels", Value: len(deviceIds)},
	)

	return nil
}
