package wspkg

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestOnIsOpenEventHandler tests the OnIsOpen event handler to ensure it doesn't cause disconnections
func TestOnIsOpenEventHandler(t *testing.T) {
	// Create a WebSocket manager
	manager, err := DefaultManager()
	require.NoError(t, err)

	// Create a WebSocketCtx with minimal setup
	wsCtx := &WebSocketCtx{
		manger:      manager,
		ClientState: NewClientState(),
	}

	// Register the event route
	wsCtx.WebSocketEventRoute()

	t.Run("OnIsOpen with valid door client should not return error", func(t *testing.T) {
		// Create a mock client
		client := &Client{
			Id: "test-client-1",
		}

		// Create door client state
		wsCtx.ClientState.CreateDoorClient(client.Id, 1, "Test Door", 1)

		// Create a mock message
		msg := &Message{
			Event: "isOpen",
			Data:  []byte(`{}`),
		}

		// Create context
		ctx := Ctx{
			manager: manager,
			client:  client,
			Data:    msg,
			ctx:     context.Background(),
		}

		// Test the event handler
		err := wsCtx.OnIsOpen(ctx)
		assert.NoError(t, err, "OnIsOpen should not return error for valid door client")
	})

	t.Run("OnIsOpen with invalid door client should not return error", func(t *testing.T) {
		// Create a mock client that doesn't exist in door state
		client := &Client{
			Id: "non-existent-client",
		}

		// Create a mock message
		msg := &Message{
			Event: "isOpen",
			Data:  []byte(`{}`),
		}

		// Create context
		ctx := Ctx{
			manager: manager,
			client:  client,
			Data:    msg,
			ctx:     context.Background(),
		}

		// Test the event handler - should not return error even if door client not found
		err := wsCtx.OnIsOpen(ctx)
		assert.NoError(t, err, "OnIsOpen should not return error even when door client not found")
	})

	t.Run("OnIsOpen should use thread-safe IsOpen method", func(t *testing.T) {
		// Create a mock client
		client := &Client{
			Id: "test-client-2",
		}

		// Create door client state
		wsCtx.ClientState.CreateDoorClient(client.Id, 2, "Test Door 2", 1)

		// Get the door client and set its state
		doorClient, err := wsCtx.ClientState.GetDoorClient(client.Id)
		require.NoError(t, err)

		// Set door to open state
		doorClient.SetIsOpen(true)

		// Create a mock message
		msg := &Message{
			Event: "isOpen",
			Data:  []byte(`{}`),
		}

		// Create context
		ctx := Ctx{
			manager: manager,
			client:  client,
			Data:    msg,
			ctx:     context.Background(),
		}

		// Test the event handler
		err = wsCtx.OnIsOpen(ctx)
		assert.NoError(t, err, "OnIsOpen should not return error")

		// Verify the door state is correctly read
		assert.True(t, doorClient.IsOpen(), "Door should be in open state")
	})
}

// TestEventRouterErrorHandling tests that event router errors don't cause disconnections
func TestEventRouterErrorHandling(t *testing.T) {
	// Create a WebSocket manager
	manager, err := DefaultManager()
	require.NoError(t, err)

	// Create a mock client
	client := &Client{
		Id: "test-client-router",
	}

	t.Run("Unknown event should return error but not crash", func(t *testing.T) {
		// Create a mock message with unknown event
		msg := &Message{
			Event: "unknownEvent",
			Data:  []byte(`{}`),
		}

		// Create context
		ctx := Ctx{
			manager: manager,
			client:  client,
			Data:    msg,
			ctx:     context.Background(),
		}

		// Test the router - should return error for unknown event
		err := manager.router.Handle(ctx)
		assert.Error(t, err, "Router should return error for unknown event")
		assert.Contains(t, err.Error(), "event not found", "Error should indicate event not found")
	})

	t.Run("Router should handle nil client gracefully", func(t *testing.T) {
		// Create a mock message
		msg := &Message{
			Event: "isOpen",
			Data:  []byte(`{}`),
		}

		// Create context with nil client
		ctx := Ctx{
			manager: manager,
			client:  nil,
			Data:    msg,
			ctx:     context.Background(),
		}

		// Test that GetClientID handles nil client
		clientID := ctx.GetClientID()
		assert.Empty(t, clientID, "GetClientID should return empty string for nil client")
	})
}

// TestClientStateThreadSafety tests thread safety of client state operations
func TestClientStateThreadSafety(t *testing.T) {
	clientState := NewClientState()

	t.Run("Concurrent door client operations should be thread-safe", func(t *testing.T) {
		// Create door client
		clientState.CreateDoorClient("test-client", 1, "Test Door", 1)

		// Get door client
		doorClient, err := clientState.GetDoorClient("test-client")
		require.NoError(t, err)

		// Test concurrent access to IsOpen method
		done := make(chan bool, 2)

		// Goroutine 1: Read IsOpen state
		go func() {
			for i := 0; i < 100; i++ {
				_ = doorClient.IsOpen()
			}
			done <- true
		}()

		// Goroutine 2: Write IsOpen state
		go func() {
			for i := 0; i < 100; i++ {
				doorClient.SetIsOpen(i%2 == 0)
			}
			done <- true
		}()

		// Wait for both goroutines to complete
		<-done
		<-done

		// Verify final state is accessible
		finalState := doorClient.IsOpen()
		assert.IsType(t, false, finalState, "IsOpen should return boolean value")
	})

	t.Run("GetDoorClient with non-existent client should return error", func(t *testing.T) {
		_, err := clientState.GetDoorClient("non-existent")
		assert.Error(t, err, "GetDoorClient should return error for non-existent client")
		assert.Contains(t, err.Error(), "door client not found", "Error should indicate client not found")
	})
}

// TestWritePumpWEventHandling tests that WritePumpW properly handles events without disconnecting
func TestWritePumpWEventHandling(t *testing.T) {
	// Create a WebSocket manager
	manager, err := DefaultManager()
	require.NoError(t, err)

	// Create a WebSocketCtx with minimal setup
	wsCtx := &WebSocketCtx{
		manger:      manager,
		ClientState: NewClientState(),
	}

	// Register the event route
	wsCtx.WebSocketEventRoute()

	t.Run("WritePumpW should handle isOpen events without disconnecting", func(t *testing.T) {
		// Create a mock client
		client := NewClient(nil) // nil connection for testing
		client.SetOnline(true)

		// Create door client state
		wsCtx.ClientState.CreateDoorClient(client.Id, 1, "Test Door", 1)

		// Create isOpen message
		isOpenMsg, err := NewMessageFromJSON("isOpen", map[string]interface{}{})
		require.NoError(t, err)

		// Send message to client's channel (simulating ReadPumpW)
		go func() {
			client.Send(isOpenMsg)
			close(client.send) // Close channel to stop WritePumpW
		}()

		// Create context for WritePumpW
		ctx := Ctx{
			manager: manager,
			client:  client,
			Data:    isOpenMsg,
			ctx:     context.Background(),
		}

		// Test event handling directly (simulating WritePumpW logic)
		err = manager.router.Handle(ctx)
		assert.NoError(t, err, "Event handling should not return error")
	})

	t.Run("WritePumpW should handle unknown events gracefully", func(t *testing.T) {
		// Create a mock client
		client := NewClient(nil) // nil connection for testing
		client.SetOnline(true)

		// Create unknown event message
		unknownMsg, err := NewMessageFromJSON("unknownEvent", map[string]interface{}{})
		require.NoError(t, err)

		// Create context for event handling
		ctx := Ctx{
			manager: manager,
			client:  client,
			Data:    unknownMsg,
			ctx:     context.Background(),
		}

		// Test event handling - should return error but not crash
		err = manager.router.Handle(ctx)
		assert.Error(t, err, "Unknown event should return error")
		assert.Contains(t, err.Error(), "event not found", "Error should indicate event not found")
	})
}
