package wspkg

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
)

// Production-ready helper functions for error handling and validation

// handleEventError provides consistent error handling across all event handlers
func (W *WebSocketCtx) handleEventError(err error, clientID string, eventType string, context map[string]interface{}) error {
	// Add standard context fields
	logFields := []Field{
		{Key: "client_id", Value: clientID},
		{Key: "event_type", Value: eventType},
		{Key: "error", Value: err},
	}

	// Add additional context fields
	for key, value := range context {
		logFields = append(logFields, Field{Key: key, Value: value})
	}

	W.manger.logger.Error("Event processing error", logFields...)

	// Return nil for non-critical errors to prevent client disconnection
	if isNonCriticalError(err) {
		return nil
	}
	return err
}

// isNonCriticalError determines if an error should cause client disconnection
func isNonCriticalError(err error) bool {
	if err == nil {
		return true
	}

	// PubSub errors shouldn't disconnect clients
	errorStr := err.Error()
	if contains(errorStr, "redis") || contains(errorStr, "pubsub") || contains(errorStr, "publish") {
		return true
	}

	// Database timeout errors shouldn't disconnect clients
	if contains(errorStr, "timeout") || contains(errorStr, "context deadline exceeded") {
		return true
	}

	return false
}

// contains checks if a string contains a substring (case-insensitive)
func contains(str, substr string) bool {
	return len(str) >= len(substr) &&
		(str == substr ||
			(len(str) > len(substr) &&
				(str[:len(substr)] == substr ||
					str[len(str)-len(substr):] == substr ||
					findInString(str, substr))))
}

func findInString(str, substr string) bool {
	for i := 0; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// writeMessageSafe provides connection state validation before WebSocket writes
func (W *WebSocketCtx) writeMessageSafe(c Ctx, msg *Message, eventType string) error {
	// Validate connection state
	if c.client == nil {
		return W.handleEventError(
			fmt.Errorf("client is nil"),
			c.GetClientID(),
			eventType,
			map[string]interface{}{"operation": "write_message"},
		)
	}

	if !c.IsClientOnline() {
		return W.handleEventError(
			fmt.Errorf("client is offline"),
			c.GetClientID(),
			eventType,
			map[string]interface{}{"operation": "write_message"},
		)
	}

	// Attempt to write message
	err := c.WriteMessage(msg)
	if err != nil {
		return W.handleEventError(
			err,
			c.GetClientID(),
			eventType,
			map[string]interface{}{"operation": "write_message"},
		)
	}

	return nil
}

// publishSafe provides graceful degradation for PubSub operations
func (W *WebSocketCtx) publishSafe(channel string, msg *Message, clientID string, eventType string) {
	err := W.Pubsub.Publish(channel, msg)
	if err != nil {
		// Log error but don't fail the operation
		W.handleEventError(
			err,
			clientID,
			eventType,
			map[string]interface{}{
				"operation": "pubsub_publish",
				"channel":   channel,
			},
		)
	}
}

func (W *WebSocketCtx) WebSocketEventRoute() {
	// Register the WebSocket event route
	W.manger.router.On("isOpen", W.OnIsOpen)
	W.manger.router.On("notify", W.OnNotify)
	W.manger.router.On("open-door", W.OnOpenDoor)
	W.manger.router.On("close-door", W.OnCloseDoor)
	W.manger.router.On("close-door-qr", W.OnCloseDoorQR)
	W.manger.router.On("new-key", W.OnNewKey)
}

func (W *WebSocketCtx) OnIsOpen(c Ctx) error {
	clientID := c.GetClientID()
	eventType := "isOpen"

	doorClient, err := W.ClientState.GetDoorClient(clientID)
	if err != nil {
		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "get_door_client",
		})
	}

	// Use thread-safe access to door client state
	isOpen := doorClient.IsOpen()
	doorID := doorClient.DoorId

	data := map[string]interface{}{"isOpen": isOpen}

	msg, err := NewMessageFromJSON(eventType, data)
	if err != nil {
		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "create_message",
			"door_id":   doorID,
		})
	}

	// Use safe message writing with connection validation
	err = W.writeMessageSafe(c, msg, eventType)
	if err != nil {
		return err // Already handled by writeMessageSafe
	}

	W.manger.logger.Debug(
		"isOpen event handled successfully",
		Field{Key: "client_id", Value: clientID},
		Field{Key: "door_id", Value: doorID},
		Field{Key: "isOpen", Value: isOpen},
	)

	// Create separate message for PubSub notification with graceful degradation
	notifyMsg, err := NewMessageFromJSON("notify", data)
	if err != nil {
		W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "create_notify_message",
			"door_id":   doorID,
		})
		return nil // Don't fail the main operation for notify message creation
	}

	// Use safe PubSub publishing with graceful degradation
	W.publishSafe(fmt.Sprintf("device-%d", doorID), notifyMsg, clientID, eventType)

	W.manger.logger.Debug(
		"isOpen event completed successfully",
		Field{Key: "client_id", Value: clientID},
		Field{Key: "door_id", Value: doorID},
		Field{Key: "isOpen", Value: isOpen},
	)
	return nil
}

func (W *WebSocketCtx) OnNotify(c Ctx) error {
	clientID := c.GetClientID()
	eventType := "notify"

	W.manger.logger.Debug("Processing notify event",
		Field{Key: "client_id", Value: clientID})

	// Validate message data
	if c.Data == nil {
		return W.handleEventError(
			fmt.Errorf("notify message data is nil"),
			clientID,
			eventType,
			map[string]interface{}{"operation": "validate_data"},
		)
	}

	// Use safe message writing with connection validation
	err := W.writeMessageSafe(c, c.Data, eventType)
	if err != nil {
		return err // Already handled by writeMessageSafe
	}

	W.manger.logger.Debug(
		"notify event handled successfully",
		Field{Key: "client_id", Value: clientID},
	)
	return nil
}

func (W *WebSocketCtx) OnOpenDoor(c Ctx) error {
	clientID := c.GetClientID()
	eventType := "open-door"

	// CRITICAL FIX: Validate door client state FIRST before any data processing
	doorClient, err := W.ClientState.GetDoorClient(clientID)
	if err != nil {
		// Send error notification but don't disconnect client
		errorMsg, msgErr := NewMessageFromJSON("notify", types.NotifyT{
			Type: "error-door",
			Msg:  "door lock not response. please restart the device",
		})
		if msgErr == nil {
			W.publishSafe(fmt.Sprintf("device-%s-notify", clientID), errorMsg, clientID, eventType)
		}

		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "get_door_client",
		})
	}

	// CRITICAL FIX: Validate processing state BEFORE data binding
	if !doorClient.CheckIsProcessing() {
		return W.handleEventError(
			fmt.Errorf("door client is not processing, cannot open door"),
			clientID,
			eventType,
			map[string]interface{}{
				"operation":     "validate_processing_state",
				"door_id":       doorClient.DoorId,
				"current_state": doorClient.GetState(),
			},
		)
	}

	// CRITICAL FIX: Proper data binding with validation
	var attendData database.CreateAttendeeParams

	err = c.Bind(&attendData)
	if err != nil {
		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "bind_attendee_data",
			"door_id":   doorClient.DoorId,
		})
	}

	// CRITICAL FIX: Convert to proper SQL types for database operation

	// Send door open command to hardware
	msg, err := NewMessageFromJSON("open-door", map[string]interface{}{})
	if err != nil {
		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "create_open_door_message",
			"door_id":   doorClient.DoorId,
		})
	}

	// Use safe message writing with connection validation
	err = W.writeMessageSafe(c, msg, eventType)
	if err != nil {
		return err // Already handled by writeMessageSafe
	}

	// Update door and QR client states atomically
	doorClient.SetIsOpen(true)
	if doorClient.qrIn != nil {
		doorClient.qrIn.SetState(code.WAITINGFORUNLOCK)
	}

	// Create notification message for real-time updates
	notifyMsg, err := NewMessageFromJSON("notify", types.NotifyT{
		Type: "notify-open-door",
		Msg:  "door open",
		Data: map[string]interface{}{
			"attendee": attendData.AttendeeID,
			"bookType": attendData.Booktype.String,
			"deviceId": attendData.DeviceID.Int64,
		},
	})
	if err != nil {
		W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "create_notify_message",
			"door_id":   doorClient.DoorId,
		})
		// Don't fail the operation for notification creation error
	} else {
		// Use safe PubSub publishing with graceful degradation
		W.publishSafe(fmt.Sprintf("device-%d-notify", doorClient.DoorId), notifyMsg, clientID, eventType)
	}

	// CRITICAL FIX: Add context timeout for database operations (5-second limit)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = W.db.CreateAttendee(ctx, attendData)
	if err != nil {
		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation":   "create_attendee_database",
			"door_id":     doorClient.DoorId,
			"attendee_id": attendData.AttendeeID,
			"book_type":   attendData.Booktype.String,
		})
	}

	W.manger.logger.Info(
		"Door opened successfully and attendance recorded",
		Field{Key: "client_id", Value: clientID},
		Field{Key: "door_id", Value: doorClient.DoorId},
		Field{Key: "attendee_id", Value: attendData.AttendeeID},
		Field{Key: "book_type", Value: attendData.Booktype.String},
	)

	return nil
}

func (W *WebSocketCtx) OnCloseDoor(c Ctx) error {
	clientID := c.GetClientID()
	eventType := "close-door"

	doorClient, err := W.ClientState.GetDoorClient(clientID)
	if err != nil {
		// Send error notification but don't disconnect client
		errorMsg, msgErr := NewMessageFromJSON("error", types.NotifyT{
			Type: "error-door",
			Msg:  "door lock not response. please restart the device",
		})
		if msgErr == nil {
			W.publishSafe(fmt.Sprintf("device-%s-notify", clientID), errorMsg, clientID, eventType)
		}

		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "get_door_client",
		})
	}

	doorID := doorClient.DoorId

	// Validate current state before making changes
	if !doorClient.IsOpen() {
		W.manger.logger.Debug(
			"Door close requested but door is already closed",
			Field{Key: "client_id", Value: clientID},
			Field{Key: "door_id", Value: doorID},
		)
		return nil // Not an error, just log and continue
	}

	// Update door states atomically
	doorClient.SetIsProcessing(false)
	doorClient.SetState(code.IDEL)
	doorClient.SetIsOpen(false)

	// Create and publish close-door-qr message to notify QR clients
	msg, err := NewMessageFromJSON("close-door-qr", map[string]interface{}{})
	if err != nil {
		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "create_close_door_message",
			"door_id":   doorID,
		})
	}

	// Use safe PubSub publishing with graceful degradation
	W.publishSafe(fmt.Sprintf("device-%d", doorID), msg, clientID, eventType)

	W.manger.logger.Info(
		"Door closed successfully",
		Field{Key: "client_id", Value: clientID},
		Field{Key: "door_id", Value: doorID},
	)

	return nil
}

func (W *WebSocketCtx) OnCloseDoorQR(c Ctx) error {
	clientID := c.GetClientID()
	eventType := "close-door-qr"

	qrClient, err := W.ClientState.GetQRClient(clientID)
	if err != nil {
		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "get_qr_client",
		})
	}

	// Validate current state before making changes
	currentState := qrClient.GetState()
	isProcessing := qrClient.CheckIsProcessing()

	// Update QR client states atomically
	qrClient.SetState(code.IDEL)
	qrClient.SetIsProcessing(false)

	newKeyMsg, err := NewMessageFromJSON("new-key", map[string]interface{}{})
	if err != nil {
		W.handleEventError(err, clientID, eventType, map[string]interface{}{})
		return nil // Don't fail the operation for notification creation error
	}
	err = W.Pubsub.Publish("device-"+strconv.FormatInt(qrClient.doorId, 10), newKeyMsg)
	if err != nil {
		W.handleEventError(err, clientID, eventType, map[string]interface{}{})
		return err // Don't fail the operation for notification creation error
	}

	W.manger.logger.Info(
		"QR client door closed successfully",
		Field{Key: "client_id", Value: clientID},
		Field{Key: "previous_state", Value: currentState},
		Field{Key: "was_processing", Value: isProcessing},
		Field{Key: "new_state", Value: code.IDEL},
		Field{Key: "new_processing", Value: false},
	)

	return nil
}

func (W *WebSocketCtx) OnNewKey(c Ctx) error {
	clientID := c.GetClientID()
	eventType := "new-key"

	qrClient, err := W.ClientState.GetQRClient(clientID)
	if err != nil {
		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "get_qr_client",
		})
	}

	doorClient := W.ClientState.GetDoorClientsByDoorId(qrClient.doorId)
	if doorClient == nil {
		return W.handleEventError(
			fmt.Errorf("door client not found for door ID %d", qrClient.doorId),
			clientID,
			eventType,
			map[string]interface{}{
				"operation": "get_door_client_by_id",
				"door_id":   qrClient.doorId,
			},
		)
	}

	// Generate JWT token with proper validation
	token, err := utils.GenerateTokenForToday(
		utils.QRCodeInfo{
			ReqDeviceId: strconv.FormatInt(qrClient.doorId, 10),
			ClientId:    qrClient.Id,
			ConnType:    qrClient.Typ,
			BranchId:    qrClient.BranchId,
			GenTime:     time.Now(),
			ExpTime:     time.Now().Add(24 * time.Hour),
		}, []byte(W.Config.JWTSecret),
	)
	if err != nil {
		errMsg, msgErr := NewMessageFromJSON("error", map[string]interface{}{
			"err": "Error generating access token",
		})
		if msgErr != nil {
			return W.handleEventError(msgErr, clientID, eventType, map[string]interface{}{
				"operation": "create_token_error_message",
				"door_id":   qrClient.doorId,
			})
		}

		W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "generate_token",
			"door_id":   qrClient.doorId,
		})

		return W.writeMessageSafe(c, errMsg, eventType)
	}

	// Create success response with token
	msgToken, err := NewMessageFromJSON("new-token", map[string]interface{}{
		"token": token,
	})
	if err != nil {
		return W.handleEventError(err, clientID, eventType, map[string]interface{}{
			"operation": "create_token_message",
			"door_id":   qrClient.doorId,
		})
	}

	// Use safe message writing with connection validation
	err = W.writeMessageSafe(c, msgToken, eventType)
	if err != nil {
		return err // Already handled by writeMessageSafe
	}

	W.manger.logger.Info(
		"QR access token generated successfully",
		Field{Key: "client_id", Value: clientID},
		Field{Key: "door_id", Value: qrClient.doorId},
		Field{Key: "qr_type", Value: qrClient.Typ},
		Field{Key: "branch_id", Value: qrClient.BranchId},
	)

	return nil
}
