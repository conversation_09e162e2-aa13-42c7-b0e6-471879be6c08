package wspkg

import (
	"encoding/json"
	"strconv"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestDoorQRAuthenticationFlow tests the authentication flow for door and QR clients
func TestDoorQRAuthenticationFlow(t *testing.T) {
	// Test data matching the user's request
	doorID := int64(9)
	branchID := 1
	apiKey := "PvLc_9yb_gUfJ4C6Zc6vSBbGOyBeSycjsyhbPL_JDGM="

	t.Run("Door Client Authentication Request Structure", func(t *testing.T) {
		// Test the exact request structure provided by the user
		authReq := types.WSConnectReq{
			ConnType: "door-lock",
			Id:       int(doorID),
			APIKey:   apiKey,
			BranchId: branchID,
		}

		// Verify request structure
		assert.Equal(t, "door-lock", authReq.ConnType, "Connection type should be door-lock")
		assert.Equal(t, 9, authReq.Id, "Device ID should be 9")
		assert.Equal(t, apiKey, authReq.APIKey, "API key should match")
		assert.Equal(t, 1, authReq.BranchId, "Branch ID should be 1")

		// Test JSON marshaling
		authBody, err := json.Marshal(authReq)
		require.NoError(t, err, "Should be able to marshal auth request")

		// Verify JSON structure
		var unmarshaled types.WSConnectReq
		err = json.Unmarshal(authBody, &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal auth request")
		assert.Equal(t, authReq, unmarshaled, "Marshaled and unmarshaled should match")

		t.Logf("✓ Door client authentication request structure validated")
		t.Logf("  Request JSON: %s", string(authBody))
	})

	t.Run("QR Client Authentication Request Structure", func(t *testing.T) {
		// Test QR-in authentication request
		qrInReq := types.QRConnectReq{
			ConnType:    "qr-in",
			ReqDeviceId: strconv.FormatInt(doorID, 10),
			BranchId:    branchID,
		}

		// Verify QR-in request structure
		assert.Equal(t, "qr-in", qrInReq.ConnType, "Connection type should be qr-in")
		assert.Equal(t, "9", qrInReq.ReqDeviceId, "Device ID should be 9")
		assert.Equal(t, 1, qrInReq.BranchId, "Branch ID should be 1")

		// Test QR-out authentication request
		qrOutReq := types.QRConnectReq{
			ConnType:    "qr-out",
			ReqDeviceId: strconv.FormatInt(doorID, 10),
			BranchId:    branchID,
		}

		// Verify QR-out request structure
		assert.Equal(t, "qr-out", qrOutReq.ConnType, "Connection type should be qr-out")
		assert.Equal(t, "9", qrOutReq.ReqDeviceId, "Device ID should be 9")
		assert.Equal(t, 1, qrOutReq.BranchId, "Branch ID should be 1")

		t.Logf("✓ QR client authentication request structures validated")
	})

	t.Run("Token Store Operations", func(t *testing.T) {
		// Test token store functionality
		tokenStore := utils.NewTokenStore(5 * time.Minute)

		// Test storing door client token
		doorToken := utils.GenerateSecureToken()
		tokenStore.StoreToken(
			doorToken,
			"test-door-client",
			"door-lock",
			branchID,
			strconv.FormatInt(doorID, 10),
			apiKey,
			time.Now().Add(2*time.Minute),
		)

		// Validate door token
		tokenInfo, valid := tokenStore.ValidateToken(doorToken)
		assert.True(t, valid, "Door token should be valid")
		assert.Equal(t, "door-lock", tokenInfo.ConnType, "Token should be for door-lock")
		assert.Equal(t, "test-door-client", tokenInfo.ClientId, "Client ID should match")

		// Test storing QR client tokens
		qrInToken := utils.GenerateSecureToken()
		tokenStore.StoreToken(
			qrInToken,
			"",
			"qr-in",
			branchID,
			strconv.FormatInt(doorID, 10),
			"",
			time.Now().Add(2*time.Minute),
		)

		qrOutToken := utils.GenerateSecureToken()
		tokenStore.StoreToken(
			qrOutToken,
			"",
			"qr-out",
			branchID,
			strconv.FormatInt(doorID, 10),
			"",
			time.Now().Add(2*time.Minute),
		)

		// Validate QR tokens
		qrInInfo, qrInValid := tokenStore.ValidateToken(qrInToken)
		assert.True(t, qrInValid, "QR-in token should be valid")
		assert.Equal(t, "qr-in", qrInInfo.ConnType, "Token should be for qr-in")

		qrOutInfo, qrOutValid := tokenStore.ValidateToken(qrOutToken)
		assert.True(t, qrOutValid, "QR-out token should be valid")
		assert.Equal(t, "qr-out", qrOutInfo.ConnType, "Token should be for qr-out")

		t.Logf("✓ Token store operations validated")
		t.Logf("  Door token: %s", doorToken[:10]+"...")
		t.Logf("  QR-in token: %s", qrInToken[:10]+"...")
		t.Logf("  QR-out token: %s", qrOutToken[:10]+"...")
	})

	t.Run("Client State Management", func(t *testing.T) {
		// Test client state operations
		clientState := wspkg.NewClientState()

		// Create door client
		clientState.CreateDoorClient("test-door-client", doorID, "Test Door Lock", branchID)

		// Verify door client exists
		doorClient, err := clientState.GetDoorClient("test-door-client")
		require.NoError(t, err, "Should be able to get door client")
		assert.Equal(t, doorID, doorClient.DoorId, "Door ID should match")
		assert.Equal(t, "Test Door Lock", doorClient.Name, "Door name should match")
		assert.Equal(t, branchID, doorClient.BranchId, "Branch ID should match")

		// Test door state operations
		assert.False(t, doorClient.IsOpen(), "Door should initially be closed")

		doorClient.SetIsOpen(true)
		assert.True(t, doorClient.IsOpen(), "Door should be open after setting")

		doorClient.SetIsOpen(false)
		assert.False(t, doorClient.IsOpen(), "Door should be closed after setting")

		// Test QR client associations
		qrInConnected, qrOutConnected, exists := clientState.GetDoorQRStatus(doorID)
		assert.True(t, exists, "Door should exist")
		assert.False(t, qrInConnected, "QR-in should not be connected initially")
		assert.False(t, qrOutConnected, "QR-out should not be connected initially")

		t.Logf("✓ Client state management validated")
		t.Logf("  Door client ID: %s", "test-door-client")
		t.Logf("  Door state: closed")
		t.Logf("  QR clients: none connected")
	})
}

// TestDoorOpenCloseFlow tests the door open/close state management
func TestDoorOpenCloseFlow(t *testing.T) {
	t.Run("Door State Management", func(t *testing.T) {
		// Create client state
		clientState := wspkg.NewClientState()

		// Test data
		doorID := int64(9)
		branchID := 1

		// Create door client
		clientState.CreateDoorClient("test-door-client", doorID, "Test Door Lock", branchID)

		// Get door client
		doorClient, err := clientState.GetDoorClient("test-door-client")
		require.NoError(t, err, "Should be able to get door client")

		// Test initial state
		assert.False(t, doorClient.IsOpen(), "Door should initially be closed")

		// Test door open
		doorClient.SetIsOpen(true)
		assert.True(t, doorClient.IsOpen(), "Door should be open after setting")

		// Test door close
		doorClient.SetIsOpen(false)
		assert.False(t, doorClient.IsOpen(), "Door should be closed after setting")

		t.Logf("✓ Door open/close flow validated")
		t.Logf("  Door ID: %d", doorID)
		t.Logf("  Final state: closed")
	})
}

// TestNotifyClientFlow tests the notify client functionality
func TestNotifyClientFlow(t *testing.T) {
	t.Run("Notify Client Request Structure", func(t *testing.T) {
		// Test notify client request structure
		notifyReq := types.NotifyClientReq{
			DeviceId: []int{9}, // Device ID 9 as specified
		}

		// Verify request structure
		assert.Len(t, notifyReq.DeviceId, 1, "Should have one device ID")
		assert.Equal(t, 9, notifyReq.DeviceId[0], "Device ID should be 9")

		// Test JSON marshaling
		notifyBody, err := json.Marshal(notifyReq)
		require.NoError(t, err, "Should be able to marshal notify request")

		// Verify JSON structure
		var unmarshaled types.NotifyClientReq
		err = json.Unmarshal(notifyBody, &unmarshaled)
		require.NoError(t, err, "Should be able to unmarshal notify request")
		assert.Equal(t, notifyReq, unmarshaled, "Marshaled and unmarshaled should match")

		t.Logf("✓ Notify client request structure validated")
		t.Logf("  Request JSON: %s", string(notifyBody))
	})
}

// TestCompleteFlowValidation tests the complete flow validation
func TestCompleteFlowValidation(t *testing.T) {
	t.Run("Complete Flow Simulation", func(t *testing.T) {
		// Test data matching user's request
		doorID := int64(9)
		branchID := 1
		apiKey := "PvLc_9yb_gUfJ4C6Zc6vSBbGOyBeSycjsyhbPL_JDGM="

		// Step 1: Validate door authentication request
		doorAuthReq := types.WSConnectReq{
			ConnType: "door-lock",
			Id:       int(doorID),
			APIKey:   apiKey,
			BranchId: branchID,
		}

		doorAuthBody, err := json.Marshal(doorAuthReq)
		require.NoError(t, err, "Should marshal door auth request")
		t.Logf("✓ Door auth request: %s", string(doorAuthBody))

		// Step 2: Validate QR-in authentication request
		qrInAuthReq := types.QRConnectReq{
			ConnType:    "qr-in",
			ReqDeviceId: strconv.FormatInt(doorID, 10),
			BranchId:    branchID,
		}

		qrInAuthBody, err := json.Marshal(qrInAuthReq)
		require.NoError(t, err, "Should marshal QR-in auth request")
		t.Logf("✓ QR-in auth request: %s", string(qrInAuthBody))

		// Step 3: Validate QR-out authentication request
		qrOutAuthReq := types.QRConnectReq{
			ConnType:    "qr-out",
			ReqDeviceId: strconv.FormatInt(doorID, 10),
			BranchId:    branchID,
		}

		qrOutAuthBody, err := json.Marshal(qrOutAuthReq)
		require.NoError(t, err, "Should marshal QR-out auth request")
		t.Logf("✓ QR-out auth request: %s", string(qrOutAuthBody))

		// Step 4: Validate notify client request
		notifyReq := types.NotifyClientReq{
			DeviceId: []int{int(doorID)},
		}

		notifyBody, err := json.Marshal(notifyReq)
		require.NoError(t, err, "Should marshal notify request")
		t.Logf("✓ Notify client request: %s", string(notifyBody))

		// Step 5: Simulate client state management
		clientState := wspkg.NewClientState()
		clientState.CreateDoorClient("door-client-9", doorID, "Door Lock 9", branchID)

		doorClient, err := clientState.GetDoorClient("door-client-9")
		require.NoError(t, err, "Should get door client")

		// Step 6: Simulate door open/close flow
		assert.False(t, doorClient.IsOpen(), "Door initially closed")

		doorClient.SetIsOpen(true)
		assert.True(t, doorClient.IsOpen(), "Door opened")
		t.Logf("✓ Door opened successfully")

		doorClient.SetIsOpen(false)
		assert.False(t, doorClient.IsOpen(), "Door closed")
		t.Logf("✓ Door closed successfully")

		// Step 7: Validate QR client status
		qrInConnected, qrOutConnected, exists := clientState.GetDoorQRStatus(doorID)
		assert.True(t, exists, "Door should exist")
		assert.False(t, qrInConnected, "QR-in not connected initially")
		assert.False(t, qrOutConnected, "QR-out not connected initially")

		t.Logf("✓ Complete flow validation successful")
		t.Logf("  Door ID: %d", doorID)
		t.Logf("  Branch ID: %d", branchID)
		t.Logf("  API Key: %s", apiKey[:10]+"...")
		t.Logf("  Final door state: closed")
		t.Logf("  QR clients: ready for connection")
	})
}
