package mocks

import (
	"sync"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
)

// MockPubSubManager implements wspkg.PubSubManager interface for testing
type MockPubSubManager struct {
	PublishCalled   bool
	LastChannel     string
	LastMessage     *wspkg.Message
	PublishError    error
	SubscribeCalled bool
	SubscribeError  error
	mu              sync.RWMutex
}

// NewMockPubSubManager creates a new mock PubSub manager
func NewMockPubSubManager() *MockPubSubManager {
	return &MockPubSubManager{}
}

// Publish mocks the Publish method
func (m *MockPubSubManager) Publish(channel string, msg *wspkg.Message) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.PublishCalled = true
	m.LastChannel = channel
	m.LastMessage = msg
	
	return m.PublishError
}

// Subscribe mocks the Subscribe method
func (m *MockPubSubManager) Subscribe(channels []string, handler func(channel string, msg *wspkg.Message)) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.SubscribeCalled = true
	
	return m.SubscribeError
}

// SubscribePattern mocks the SubscribePattern method
func (m *MockPubSubManager) SubscribePattern(pattern string, handler func(channel string, msg wspkg.Message)) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	return m.SubscribeError
}

// Close mocks the Close method
func (m *MockPubSubManager) Close() error {
	return nil
}

// Reset resets the mock state
func (m *MockPubSubManager) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.PublishCalled = false
	m.LastChannel = ""
	m.LastMessage = nil
	m.PublishError = nil
	m.SubscribeCalled = false
	m.SubscribeError = nil
}

// SetPublishError sets the error to return from Publish
func (m *MockPubSubManager) SetPublishError(err error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.PublishError = err
}

// SetSubscribeError sets the error to return from Subscribe
func (m *MockPubSubManager) SetSubscribeError(err error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.SubscribeError = err
}

// GetLastPublish returns the last published channel and message
func (m *MockPubSubManager) GetLastPublish() (string, *wspkg.Message) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.LastChannel, m.LastMessage
}
