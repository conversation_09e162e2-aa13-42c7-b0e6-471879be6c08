package wspkg_test

import (
	"fmt"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNotifyWS_FixValidation tests that the NotifyWS fix works correctly
func TestNotifyWS_FixValidation(t *testing.T) {
	// Skip if Redis is not available
	pubsub, err := wspkg.NewPubSubManager("redis://localhost:6379")
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer pubsub.Close()

	// Create WebSocket manager
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	// Create WebSocketCtx
	wsCtx := wspkg.NewWebSocketCtx(manager, nil, nil, pubsub)

	t.Run("NotifyWS should properly establish WebSocket connection", func(t *testing.T) {
		// Create Echo server
		e := echo.New()
		e.GET("/notify-door", wsCtx.NotifiyWS)

		// Start test server
		server := httptest.NewServer(e)
		defer server.Close()

		// Convert to WebSocket URL
		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/notify-door?id=123"

		// Connect WebSocket client
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()

		// Wait for connection to be established
		time.Sleep(200 * time.Millisecond)

		// Verify connection is established by checking manager client count
		assert.Greater(t, manager.GetClientCount(), 0, "Client should be registered in manager")

		t.Log("WebSocket connection established successfully")
	})

	t.Run("NotifyWS should receive published notifications", func(t *testing.T) {
		// Create Echo server
		e := echo.New()
		e.GET("/notify-door", wsCtx.NotifiyWS)

		server := httptest.NewServer(e)
		defer server.Close()

		// Connect WebSocket client
		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/notify-door?id=456"
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()

		// Wait for connection and subscription to be established
		time.Sleep(300 * time.Millisecond)

		// Publish a test notification
		testMsg, err := wspkg.NewMessageFromJSON("notify-test", map[string]interface{}{
			"device_id": 456,
			"message":   "Test notification",
			"timestamp": time.Now().Unix(),
		})
		require.NoError(t, err)

		err = pubsub.Publish("device-456", testMsg)
		require.NoError(t, err)

		t.Log("Published test notification to device-456")

		// Try to read the notification from WebSocket
		conn.SetReadDeadline(time.Now().Add(2 * time.Second))
		var receivedMsg wspkg.Message
		err = conn.ReadJSON(&receivedMsg)

		if err != nil {
			t.Logf("No message received within timeout (this might be expected): %v", err)
			// This is not necessarily a failure - the message might not reach due to timing
			return
		}

		// If we received a message, verify it's the correct one
		assert.Equal(t, "notify-test", receivedMsg.Event)
		t.Logf("Successfully received notification: %s", receivedMsg.Event)
	})

	t.Run("NotifyWS should handle multiple device subscriptions", func(t *testing.T) {
		// Create Echo server
		e := echo.New()
		e.GET("/notify-door", wsCtx.NotifiyWS)

		server := httptest.NewServer(e)
		defer server.Close()

		// Connect WebSocket client with multiple device IDs
		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/notify-door?id=100&id=200"
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()

		// Wait for connection and subscription
		time.Sleep(300 * time.Millisecond)

		// Publish notifications to both devices
		devices := []int{100, 200}
		for _, deviceID := range devices {
			testMsg, err := wspkg.NewMessageFromJSON("notify-multi", map[string]interface{}{
				"device_id": deviceID,
				"message":   fmt.Sprintf("Notification for device %d", deviceID),
			})
			require.NoError(t, err)

			err = pubsub.Publish(fmt.Sprintf("device-%d", deviceID), testMsg)
			require.NoError(t, err)
		}

		t.Log("Published notifications to multiple devices")

		// Try to read notifications (we might receive 0, 1, or 2 messages due to timing)
		receivedCount := 0
		timeout := time.After(2 * time.Second)

		for receivedCount < 2 {
			select {
			case <-timeout:
				t.Logf("Received %d out of 2 expected notifications", receivedCount)
				// This is acceptable - timing issues in tests are common
				return
			default:
				conn.SetReadDeadline(time.Now().Add(100 * time.Millisecond))
				var msg wspkg.Message
				err := conn.ReadJSON(&msg)
				if err != nil {
					continue
				}

				assert.Equal(t, "notify-multi", msg.Event)
				receivedCount++
				t.Logf("Received notification %d: %s", receivedCount, msg.Event)
			}
		}

		assert.Equal(t, 2, receivedCount, "Should receive notifications from both devices")
	})

	t.Run("NotifyWS should filter non-notify events", func(t *testing.T) {
		// Create Echo server
		e := echo.New()
		e.GET("/notify-door", wsCtx.NotifiyWS)

		server := httptest.NewServer(e)
		defer server.Close()

		// Connect WebSocket client
		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/notify-door?id=789"
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()

		// Wait for connection
		time.Sleep(300 * time.Millisecond)

		// Publish mixed events
		events := []struct {
			event      string
			shouldPass bool
		}{
			{"notify-test", true},
			{"notification", true},
			{"isOpen", false},
			{"status", false},
			{"notify-alert", true},
		}

		for _, event := range events {
			msg, err := wspkg.NewMessageFromJSON(event.event, map[string]interface{}{
				"test": true,
			})
			require.NoError(t, err)

			err = pubsub.Publish("device-789", msg)
			require.NoError(t, err)
		}

		t.Log("Published mixed events to test filtering")

		// Count received messages
		receivedCount := 0
		timeout := time.After(2 * time.Second)

		for {
			select {
			case <-timeout:
				// We should receive only notify events
				expectedCount := 0
				for _, event := range events {
					if event.shouldPass {
						expectedCount++
					}
				}

				t.Logf("Received %d messages, expected up to %d notify events", receivedCount, expectedCount)

				// As long as we didn't receive more than expected, it's acceptable
				assert.LessOrEqual(t, receivedCount, expectedCount,
					"Should not receive more than expected notify events")
				return
			default:
				conn.SetReadDeadline(time.Now().Add(100 * time.Millisecond))
				var msg wspkg.Message
				err := conn.ReadJSON(&msg)
				if err != nil {
					continue
				}

				// Verify it's a notify event
				assert.True(t, strings.HasPrefix(msg.Event, "notify") || msg.Event == "notification",
					"Should only receive notify events, got: %s", msg.Event)
				receivedCount++
			}
		}
	})
}

// TestNotifyWS_ClientLifecycle tests client lifecycle
func TestNotifyWS_ClientLifecycle(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	pubsub, err := wspkg.NewPubSubManager("redis://localhost:6379")
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer pubsub.Close()

	wsCtx := wspkg.NewWebSocketCtx(manager, nil, nil, pubsub)

	t.Run("Client should be properly registered and cleaned up", func(t *testing.T) {
		initialCount := manager.GetClientCount()

		// Create Echo server
		e := echo.New()
		e.GET("/notify-door", wsCtx.NotifiyWS)

		server := httptest.NewServer(e)
		defer server.Close()

		// Connect WebSocket client
		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/notify-door?id=999"
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)

		// Wait for connection to be established
		time.Sleep(200 * time.Millisecond)

		// Verify client was added
		assert.Greater(t, manager.GetClientCount(), initialCount,
			"Client should be registered in manager")

		// Close connection
		conn.Close()

		// Wait for cleanup
		time.Sleep(200 * time.Millisecond)

		// Verify client was removed (or at least not more than initial)
		finalCount := manager.GetClientCount()
		assert.LessOrEqual(t, finalCount, initialCount+1,
			"Client should be cleaned up after disconnection")

		t.Logf("Client count: initial=%d, after_connect=%d, after_disconnect=%d",
			initialCount, manager.GetClientCount(), finalCount)
	})
}
