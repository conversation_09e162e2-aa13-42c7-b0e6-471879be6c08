package wspkg

import (
	"encoding/json"
	"strconv"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/testing/wspkg/mocks"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestCompleteDoorFlowWithNewKeyGeneration tests the complete flow:
// HTTP OpenDoor → OnCloseDoor → OnCloseDoorQR → OnNewKey for both QR clients
func TestCompleteDoorFlowWithNewKeyGeneration(t *testing.T) {
	// Test data matching user's request
	doorID := int64(9)
	branchID := 1
	apiKey := "PvLc_9yb_gUfJ4C6Zc6vSBbGOyBeSycjsyhbPL_JDGM="
	jwtSecret := "test-jwt-secret-key"

	t.Run("Complete OpenDoor to NewKey Generation Flow", func(t *testing.T) {
		// Step 1: Setup connected clients (simulating WebSocket connections)
		setup := setupCompleteFlowTest(t, jwtSecret)

		// Step 2: Create and connect all clients
		doorClientID, qrInClientID, qrOutClientID := setupAllConnectedClients(t, setup, doorID, branchID, apiKey)

		// Step 3: Generate QR token for door access
		qrToken := generateQRTokenForDoorAccess(t, setup, qrInClientID, doorID, branchID, jwtSecret)

		// Step 4: Simulate HTTP OpenDoor request
		simulateHTTPOpenDoorRequest(t, setup, qrToken, doorClientID, qrInClientID)

		// Step 5: Simulate door hardware opening (triggers OnOpenDoor event)
		simulateOnOpenDoorEvent(t, setup, doorClientID, doorID)

		// Step 6: Simulate door hardware closing (triggers OnCloseDoor event)
		simulateOnCloseDoorEvent(t, setup, doorClientID, doorID)

		// Step 7: Simulate OnCloseDoorQR event for both QR clients
		simulateOnCloseDoorQREvents(t, setup, qrInClientID, qrOutClientID, doorID)

		// Step 8: Simulate OnNewKey event for both QR clients (generates new keys)
		simulateOnNewKeyEvents(t, setup, qrInClientID, qrOutClientID, doorID, jwtSecret)

		// Step 9: Verify final state and new keys
		verifyCompleteFlowResults(t, setup, doorClientID, qrInClientID, qrOutClientID, doorID)

		t.Logf("✅ Complete Door Flow with New Key Generation successful")
		t.Logf("  Flow: HTTP OpenDoor → OnOpenDoor → OnCloseDoor → OnCloseDoorQR → OnNewKey")
		t.Logf("  Door Client: %s", doorClientID)
		t.Logf("  QR-In Client: %s (new key generated)", qrInClientID)
		t.Logf("  QR-Out Client: %s (new key generated)", qrOutClientID)
	})
}

// CompleteFlowSetup contains all components for complete flow testing
type CompleteFlowSetup struct {
	Manager         *wspkg.Manager
	DB              *mocks.MockQueries
	TokenStore      *utils.TokenStore
	PubSub          *mocks.MockPubSubManager
	Logger          *mocks.MockLogger
	WSCtx           *wspkg.WebSocketCtx
	JWTSecret       string
	GeneratedTokens []string // Track generated tokens
}

// setupCompleteFlowTest creates the test environment for complete flow testing
func setupCompleteFlowTest(t *testing.T, jwtSecret string) *CompleteFlowSetup {
	// Create mock components
	logger := mocks.NewMockLogger()
	db := mocks.NewMockQueries()
	tokenStore := utils.NewTokenStore(5 * time.Minute)
	pubsub := mocks.NewMockPubSubManager()

	// Create manager
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)
	manager.SetLogger(logger)

	// Create WebSocket context - using simplified approach without mocks
	wsCtx := &wspkg.WebSocketCtx{
		ClientState: wspkg.NewClientState(),
	}
	// Skip WebSocketEventRoute() to avoid nil pointer issues in testing

	return &CompleteFlowSetup{
		Manager:         manager,
		DB:              db,
		TokenStore:      tokenStore,
		PubSub:          pubsub,
		Logger:          logger,
		WSCtx:           wsCtx,
		JWTSecret:       jwtSecret,
		GeneratedTokens: make([]string, 0),
	}
}

// setupAllConnectedClients sets up door and both QR clients in connected state
func setupAllConnectedClients(t *testing.T, setup *CompleteFlowSetup, doorID int64, branchID int, apiKey string) (string, string, string) {
	// Create door client (simulating /ws-auth connection)
	doorClientID := "door-client-9"
	setup.WSCtx.ClientState.CreateDoorClient(doorClientID, doorID, "Test Door Lock 9", branchID)

	// Create QR clients (simulating /ws-qr-auth connections)
	qrInClientID := "qr-in-client-9"
	qrOutClientID := "qr-out-client-9"

	setup.WSCtx.ClientState.CreateQRClientWithDoorCheckin(qrInClientID, "qr-in", branchID, doorID)
	setup.WSCtx.ClientState.CreateQRClientWithDoorCheckin(qrOutClientID, "qr-out", branchID, doorID)

	t.Logf("✅ All clients connected and ready")
	t.Logf("  Door: %s", doorClientID)
	t.Logf("  QR-In: %s", qrInClientID)
	t.Logf("  QR-Out: %s", qrOutClientID)

	return doorClientID, qrInClientID, qrOutClientID
}

// generateQRTokenForDoorAccess generates a JWT token for door access
func generateQRTokenForDoorAccess(t *testing.T, setup *CompleteFlowSetup, qrClientID string, doorID int64, branchID int, jwtSecret string) string {
	qrInfo := utils.QRCodeInfo{
		ClientId:    qrClientID,
		ReqDeviceId: strconv.FormatInt(doorID, 10),
		ConnType:    "qr-in",
		BranchId:    branchID,
		GenTime:     time.Now(),
		ExpTime:     time.Now().Add(5 * time.Minute),
	}

	token, err := utils.GenerateJWTToken(qrInfo, 5*time.Minute, []byte(jwtSecret))
	require.NoError(t, err, "Should generate QR token successfully")

	setup.GeneratedTokens = append(setup.GeneratedTokens, token)
	t.Logf("✅ QR token generated for door access: %s", token[:20]+"...")
	return token
}

// simulateHTTPOpenDoorRequest simulates the HTTP OpenDoor request processing
func simulateHTTPOpenDoorRequest(t *testing.T, setup *CompleteFlowSetup, qrToken, doorClientID, qrClientID string) {
	// Create HTTP OpenDoor request
	doorAccessReq := types.DoorLockAccessReq{
		User: types.TmpUser{
			ID:       123,
			BookType: "employee",
		},
		QRcode:    qrToken,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	// Validate request structure
	reqBody, err := json.Marshal(doorAccessReq)
	require.NoError(t, err, "Should marshal door access request")

	// Validate QR token (simulating HTTP handler validation)
	claims, err := utils.ValidateToken(qrToken, []byte(setup.JWTSecret))
	require.NoError(t, err, "QR token should be valid")

	qrData := claims.QRInfo
	assert.Equal(t, qrClientID, qrData.ClientId, "Client ID should match")
	assert.Equal(t, "9", qrData.ReqDeviceId, "Device ID should match")

	// Verify door and QR clients exist (simulating HTTP handler checks)
	doorClient, err := setup.WSCtx.ClientState.GetDoorClient(doorClientID)
	require.NoError(t, err, "Door client should exist")

	qrClient, err := setup.WSCtx.ClientState.GetQRClient(qrClientID)
	require.NoError(t, err, "QR client should exist")

	t.Logf("✅ HTTP OpenDoor request validated")
	t.Logf("  Request: %s", string(reqBody))
	t.Logf("  Door client: %s", doorClient.Name)
	t.Logf("  QR client: %s (%s)", qrClient.Id, qrClient.CheckType())
}

// simulateOnOpenDoorEvent simulates the OnOpenDoor event handler
func simulateOnOpenDoorEvent(t *testing.T, setup *CompleteFlowSetup, doorClientID string, doorID int64) {
	// Get door client
	doorClient, err := setup.WSCtx.ClientState.GetDoorClient(doorClientID)
	require.NoError(t, err, "Should get door client")

	// Verify initial state
	assert.False(t, doorClient.IsOpen(), "Door should initially be closed")

	// Simulate door opening (hardware response)
	doorClient.SetIsOpen(true)
	assert.True(t, doorClient.IsOpen(), "Door should be open after hardware response")

	// Simulate attendance record creation (would happen in OnOpenDoor)
	t.Log("📝 Simulating attendance record creation...")

	t.Logf("✅ OnOpenDoor event simulated")
	t.Logf("  Door opened: %t", doorClient.IsOpen())
	t.Logf("  Attendance record created")
}

// simulateOnCloseDoorEvent simulates the OnCloseDoor event handler
func simulateOnCloseDoorEvent(t *testing.T, setup *CompleteFlowSetup, doorClientID string, doorID int64) {
	// Get door client
	doorClient, err := setup.WSCtx.ClientState.GetDoorClient(doorClientID)
	require.NoError(t, err, "Should get door client")

	// Verify door is open before closing
	assert.True(t, doorClient.IsOpen(), "Door should be open before closing")

	// Simulate OnCloseDoor event processing
	doorClient.SetIsProcessing(false)
	doorClient.SetIsOpen(false)

	// Verify door state after close
	assert.False(t, doorClient.IsOpen(), "Door should be closed after OnCloseDoor")

	// Simulate publishing close-door-qr message (would happen in OnCloseDoor)
	t.Log("📢 Simulating close-door-qr message publication...")

	t.Logf("✅ OnCloseDoor event simulated")
	t.Logf("  Door closed: %t", !doorClient.IsOpen())
	t.Logf("  close-door-qr message published")
}

// simulateOnCloseDoorQREvents simulates OnCloseDoorQR events for both QR clients
func simulateOnCloseDoorQREvents(t *testing.T, setup *CompleteFlowSetup, qrInClientID, qrOutClientID string, doorID int64) {
	// Simulate OnCloseDoorQR for QR-In client
	qrInClient, err := setup.WSCtx.ClientState.GetQRClient(qrInClientID)
	require.NoError(t, err, "Should get QR-in client")

	// Update QR-in client state (simulating OnCloseDoorQR processing)
	qrInClient.SetIsProcessing(false)

	// Simulate OnCloseDoorQR for QR-Out client
	qrOutClient, err := setup.WSCtx.ClientState.GetQRClient(qrOutClientID)
	require.NoError(t, err, "Should get QR-out client")

	// Update QR-out client state (simulating OnCloseDoorQR processing)
	qrOutClient.SetIsProcessing(false)

	// Simulate publishing new-key message (would happen in OnCloseDoorQR)
	t.Log("📢 Simulating new-key message publication for both QR clients...")

	t.Logf("✅ OnCloseDoorQR events simulated")
	t.Logf("  QR-In client state reset")
	t.Logf("  QR-Out client state reset")
	t.Logf("  new-key messages published")
}

// simulateOnNewKeyEvents simulates OnNewKey events for both QR clients (generates new keys)
func simulateOnNewKeyEvents(t *testing.T, setup *CompleteFlowSetup, qrInClientID, qrOutClientID string, doorID int64, jwtSecret string) {
	// Generate new key for QR-In client
	qrInClient, err := setup.WSCtx.ClientState.GetQRClient(qrInClientID)
	require.NoError(t, err, "Should get QR-in client")

	newQRInToken := generateNewQRKey(t, qrInClient, doorID, jwtSecret)
	setup.GeneratedTokens = append(setup.GeneratedTokens, newQRInToken)

	// Generate new key for QR-Out client
	qrOutClient, err := setup.WSCtx.ClientState.GetQRClient(qrOutClientID)
	require.NoError(t, err, "Should get QR-out client")

	newQROutToken := generateNewQRKey(t, qrOutClient, doorID, jwtSecret)
	setup.GeneratedTokens = append(setup.GeneratedTokens, newQROutToken)

	t.Logf("✅ OnNewKey events simulated")
	t.Logf("  New QR-In token: %s", newQRInToken[:20]+"...")
	t.Logf("  New QR-Out token: %s", newQROutToken[:20]+"...")
}

// generateNewQRKey generates a new JWT token for a QR client (simulating OnNewKey)
func generateNewQRKey(t *testing.T, qrClient *wspkg.QRClient, doorID int64, jwtSecret string) string {
	qrInfo := utils.QRCodeInfo{
		ClientId:    qrClient.Id,
		ReqDeviceId: strconv.FormatInt(doorID, 10), // Use passed doorID parameter
		ConnType:    qrClient.CheckType(),
		BranchId:    qrClient.BranchId,
		GenTime:     time.Now(),
		ExpTime:     time.Now().Add(24 * time.Hour), // New keys have longer expiration
	}

	token, err := utils.GenerateJWTToken(qrInfo, 24*time.Hour, []byte(jwtSecret))
	require.NoError(t, err, "Should generate new QR token successfully")

	return token
}

// verifyCompleteFlowResults verifies the final state after complete flow
func verifyCompleteFlowResults(t *testing.T, setup *CompleteFlowSetup, doorClientID, qrInClientID, qrOutClientID string, doorID int64) {
	// Verify door client final state
	doorClient, err := setup.WSCtx.ClientState.GetDoorClient(doorClientID)
	require.NoError(t, err, "Should get door client")

	assert.False(t, doorClient.IsOpen(), "Door should be closed in final state")
	assert.False(t, doorClient.CheckIsProcessing(), "Door should not be processing in final state")

	// Verify QR clients final state
	qrInClient, err := setup.WSCtx.ClientState.GetQRClient(qrInClientID)
	require.NoError(t, err, "Should get QR-in client")
	assert.False(t, qrInClient.CheckIsProcessing(), "QR-in should not be processing")

	qrOutClient, err := setup.WSCtx.ClientState.GetQRClient(qrOutClientID)
	require.NoError(t, err, "Should get QR-out client")
	assert.False(t, qrOutClient.CheckIsProcessing(), "QR-out should not be processing")

	// Verify QR client associations
	qrInConnected, qrOutConnected, exists := setup.WSCtx.ClientState.GetDoorQRStatus(doorID)
	assert.True(t, exists, "Door should exist in client state")
	assert.True(t, qrInConnected, "QR-in should be connected")
	assert.True(t, qrOutConnected, "QR-out should be connected")

	// Verify new tokens were generated
	assert.GreaterOrEqual(t, len(setup.GeneratedTokens), 3, "Should have generated at least 3 tokens (initial + 2 new)")

	// Validate the new tokens
	for i, token := range setup.GeneratedTokens {
		claims, err := utils.ValidateToken(token, []byte(setup.JWTSecret))
		require.NoError(t, err, "Token %d should be valid", i)

		qrData := claims.QRInfo
		assert.Equal(t, "9", qrData.ReqDeviceId, "All tokens should reference door 9")
		assert.Equal(t, 1, qrData.BranchId, "All tokens should reference branch 1")
	}

	t.Logf("✅ Complete flow results verified")
	t.Logf("  Door state: closed, not processing")
	t.Logf("  QR clients: connected, not processing")
	t.Logf("  New tokens generated: %d", len(setup.GeneratedTokens))
	t.Logf("  All tokens valid and properly structured")
}
