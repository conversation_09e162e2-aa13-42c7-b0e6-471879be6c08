package wspkg_test

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"testing"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/testing/wspkg/mocks"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestEventRouteRegistration tests the WebSocketEventRoute function
func TestEventRouteRegistration(t *testing.T) {
	t.Run("EventRouteRegistration", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := mocks.NewMockQueries()
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := mocks.NewMockPubSubManager()

		// Create a wrapper that implements the database.Queries interface
		dbWrapper := &database.Queries{}

		wsCtx := wspkg.NewWebSocketCtx(manager, dbWrapper, mockTokenStore, mockPubSub)

		// Test that event route registration doesn't panic
		require.NotPanics(t, func() {
			wsCtx.WebSocketEventRoute()
		})
	})
}

// TestOnIsOpenFunction tests the OnIsOpen event handler
func TestOnIsOpenFunction(t *testing.T) {
	t.Run("SuccessfulIsOpenEvent", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create door client
		clientID := "test-door-client"
		doorID := int64(123)
		wsCtx.ClientState.CreateDoorClient(clientID, doorID, "Test Door", 1)

		// Create mock context
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = clientID

		mockMessage := wspkg.NewMessage("isOpen", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnIsOpen
		err = wsCtx.OnIsOpen(ctx)
		assert.NoError(t, err)

		// Verify PubSub publish was called
		assert.True(t, mockPubSub.PublishCalled)
		assert.Equal(t, fmt.Sprintf("device-%d", doorID), mockPubSub.LastChannel)
	})

	t.Run("DoorClientNotFound", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create mock context with non-existent client
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = "non-existent-client"

		mockMessage := wspkg.NewMessage("isOpen", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnIsOpen with non-existent door client
		err = wsCtx.OnIsOpen(ctx)
		assert.NoError(t, err) // Should not return error to prevent disconnection

		// Verify PubSub publish was not called
		assert.False(t, mockPubSub.PublishCalled)
	})

	t.Run("ConcurrentIsOpenAccess", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create door client
		clientID := "test-door-client"
		doorID := int64(123)
		wsCtx.ClientState.CreateDoorClient(clientID, doorID, "Test Door", 1)

		// Create mock context
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = clientID

		mockMessage := wspkg.NewMessage("isOpen", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test concurrent access
		var wg sync.WaitGroup
		var errors []error
		var mu sync.Mutex

		for i := 0; i < 10; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				err := wsCtx.OnIsOpen(ctx)
				mu.Lock()
				if err != nil {
					errors = append(errors, err)
				}
				mu.Unlock()
			}()
		}

		wg.Wait()

		// All calls should succeed
		assert.Empty(t, errors)
	})
}

// TestOnNotifyFunction tests the OnNotify event handler
func TestOnNotifyFunction(t *testing.T) {
	t.Run("SuccessfulNotifyEvent", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create mock context
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = "test-client"

		testData := map[string]interface{}{"message": "test notification"}
		dataBytes, _ := json.Marshal(testData)
		mockMessage := wspkg.NewMessage("notify", dataBytes)
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnNotify
		err = wsCtx.OnNotify(ctx)
		// Note: This will likely fail due to nil connection, but we're testing the logic
		// In a real test, we'd need a proper WebSocket connection or mock
	})
}

// TestOnOpenDoorFunction tests the OnOpenDoor event handler
func TestOnOpenDoorFunction(t *testing.T) {
	t.Run("SuccessfulDoorOpen", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{
			CreateAttendeeFunc: func(ctx context.Context, arg database.CreateAttendeeParams) error {
				return nil
			},
		}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create door client and set it to processing state
		clientID := "test-door-client"
		doorID := int64(123)
		wsCtx.ClientState.CreateDoorClient(clientID, doorID, "Test Door", 1)

		doorClient, err := wsCtx.ClientState.GetDoorClient(clientID)
		require.NoError(t, err)
		doorClient.SetIsProcessing(true)

		// Create QR client and associate it
		qrClientID := "test-qr-client"
		wsCtx.ClientState.CreateQRClient(qrClientID, "qr-in", 1, doorID)
		err = wsCtx.ClientState.SafelyAssociateQRClient(doorID, qrClientID, "qr-in")
		require.NoError(t, err)

		// Create mock context with attendee data
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = clientID

		attendeeData := database.CreateAttendeeParams{
			AttendeeID: 456,
			Booktype:   "entry",
			DeviceID:   doorID,
		}
		dataBytes, _ := json.Marshal(attendeeData)
		mockMessage := wspkg.NewMessage("open-door", dataBytes)
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnOpenDoor - this will fail due to nil connection but tests the logic
		err = wsCtx.OnOpenDoor(ctx)
		// We expect an error due to nil connection, but the logic should be executed
	})

	t.Run("DoorClientNotProcessing", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create door client but don't set it to processing
		clientID := "test-door-client"
		doorID := int64(123)
		wsCtx.ClientState.CreateDoorClient(clientID, doorID, "Test Door", 1)

		// Create mock context
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = clientID

		mockMessage := wspkg.NewMessage("open-door", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnOpenDoor with non-processing door
		err = wsCtx.OnOpenDoor(ctx)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "door client is not processing")
	})

	t.Run("DatabaseError", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{
			CreateAttendeeFunc: func(ctx context.Context, arg database.CreateAttendeeParams) error {
				return errors.New("database connection failed")
			},
		}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create door client and set it to processing state
		clientID := "test-door-client"
		doorID := int64(123)
		wsCtx.ClientState.CreateDoorClient(clientID, doorID, "Test Door", 1)

		doorClient, err := wsCtx.ClientState.GetDoorClient(clientID)
		require.NoError(t, err)
		doorClient.SetIsProcessing(true)

		// Create mock context
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = clientID

		mockMessage := wspkg.NewMessage("open-door", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnOpenDoor with database error
		// This will fail due to nil connection, but we're testing the error handling logic
	})
}

// TestOnCloseDoorFunction tests the OnCloseDoor event handler
func TestOnCloseDoorFunction(t *testing.T) {
	t.Run("SuccessfulDoorClose", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create door client
		clientID := "test-door-client"
		doorID := int64(123)
		wsCtx.ClientState.CreateDoorClient(clientID, doorID, "Test Door", 1)

		doorClient, err := wsCtx.ClientState.GetDoorClient(clientID)
		require.NoError(t, err)

		// Set initial state
		doorClient.SetIsProcessing(true)
		doorClient.SetIsOpen(true)

		// Create mock context
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = clientID

		mockMessage := wspkg.NewMessage("close-door", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnCloseDoor
		err = wsCtx.OnCloseDoor(ctx)
		// Will fail due to nil connection, but tests state changes

		// Verify state changes
		assert.False(t, doorClient.CheckIsProcessing())
		assert.Equal(t, code.IDEL, doorClient.GetState())
		assert.False(t, doorClient.IsOpen())
	})

	t.Run("DoorClientNotFound", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create mock context with non-existent client
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = "non-existent-client"

		mockMessage := wspkg.NewMessage("close-door", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnCloseDoor with non-existent door client
		err = wsCtx.OnCloseDoor(ctx)
		assert.NoError(t, err) // Should not return error to prevent disconnection
	})
}

// TestOnCloseDoorQRFunction tests the OnCloseDoorQR event handler
func TestOnCloseDoorQRFunction(t *testing.T) {
	t.Run("SuccessfulQRClose", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create QR client
		qrClientID := "test-qr-client"
		doorID := int64(123)
		wsCtx.ClientState.CreateQRClient(qrClientID, "qr-in", 1, doorID)

		qrClient, err := wsCtx.ClientState.GetQRClient(qrClientID)
		require.NoError(t, err)

		// Set initial state
		qrClient.SetIsProcessing(true)
		qrClient.SetState(code.PROCESSING)

		// Create mock context
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = qrClientID

		mockMessage := wspkg.NewMessage("close-door-qr", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnCloseDoorQR
		err = wsCtx.OnCloseDoorQR(ctx)
		assert.NoError(t, err)

		// Verify state changes
		assert.False(t, qrClient.CheckIsProcessing())
		assert.Equal(t, code.IDEL, qrClient.GetState())
	})

	t.Run("QRClientNotFound", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create mock context with non-existent QR client
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = "non-existent-qr-client"

		mockMessage := wspkg.NewMessage("close-door-qr", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnCloseDoorQR with non-existent QR client
		err = wsCtx.OnCloseDoorQR(ctx)
		assert.NoError(t, err) // Should not return error
	})
}

// TestOnNewKeyFunction tests the OnNewKey event handler
func TestOnNewKeyFunction(t *testing.T) {
	t.Run("SuccessfulTokenGeneration", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create door client with security key
		doorClientID := "test-door-client"
		doorID := int64(123)
		wsCtx.ClientState.CreateDoorClient(doorClientID, doorID, "Test Door", 1)

		doorClient, err := wsCtx.ClientState.GetDoorClient(doorClientID)
		require.NoError(t, err)
		doorClient.SetSecKey("test-secret-key")

		// Create QR client
		qrClientID := "test-qr-client"
		wsCtx.ClientState.CreateQRClient(qrClientID, "qr-in", 1, doorID)

		// Create mock context
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = qrClientID

		mockMessage := wspkg.NewMessage("new-key", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnNewKey - will fail due to nil connection but tests token generation logic
		err = wsCtx.OnNewKey(ctx)
		// Expected to fail due to nil connection, but token generation logic should execute
	})

	t.Run("QRClientNotFound", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create mock context with non-existent QR client
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = "non-existent-qr-client"

		mockMessage := wspkg.NewMessage("new-key", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnNewKey with non-existent QR client
		err = wsCtx.OnNewKey(ctx)
		assert.NoError(t, err) // Should not return error
	})

	t.Run("DoorClientNotFound", func(t *testing.T) {
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		mockDB := &mocks.MockQueries{}
		mockTokenStore := &utils.TokenStore{}
		mockPubSub := &mocks.MockPubSubManager{}

		wsCtx := wspkg.NewWebSocketCtx(manager, mockDB, mockTokenStore, mockPubSub)

		// Create QR client without corresponding door client
		qrClientID := "test-qr-client"
		doorID := int64(999) // Non-existent door ID
		wsCtx.ClientState.CreateQRClient(qrClientID, "qr-in", 1, doorID)

		// Create mock context
		mockClient := wspkg.NewClient(nil)
		mockClient.Id = qrClientID

		mockMessage := wspkg.NewMessage("new-key", []byte(`{}`))
		ctx := wspkg.NewCtx(manager, mockClient, nil, mockMessage, context.Background())

		// Test OnNewKey with non-existent door client
		err = wsCtx.OnNewKey(ctx)
		assert.NoError(t, err) // Should not return error
	})
}
