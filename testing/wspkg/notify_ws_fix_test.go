package wspkg_test

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNotifyWS_MessageFlow tests the complete message flow from subscription to notification delivery
func TestNotifyWS_MessageFlow(t *testing.T) {
	// Create WebSocket manager
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	// Create PubSub manager
	pubsub, err := wspkg.NewPubSubManager("redis://localhost:6379")
	require.NoError(t, err)
	defer pubsub.Close()

	// Create WebSocketCtx
	wsCtx := wspkg.NewWebSocketCtx(manager, nil, nil, pubsub)

	// Register event routes
	wsCtx.WebSocketEventRoute()

	t.Run("NotifyWS should properly set up client and receive notifications", func(t *testing.T) {
		// Create Echo context with device IDs
		e := echo.New()
		req := httptest.NewRequest(http.MethodGet, "/notify-door?id=1&id=2", nil)
		rec := httptest.NewRecorder()
		_ = e.NewContext(req, rec)

		// Create a test server for WebSocket upgrade
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Parse query parameters
			deviceIds := r.URL.Query()["id"]
			assert.Equal(t, []string{"1", "2"}, deviceIds)

			// Upgrade to WebSocket
			upgrader := websocket.Upgrader{
				CheckOrigin: func(r *http.Request) bool { return true },
			}
			conn, err := upgrader.Upgrade(w, r, nil)
			require.NoError(t, err)
			defer conn.Close()

			// Create client and test subscription
			client := wspkg.NewClient(conn)
			client.SetOnline(true)

			// Add client to manager
			isAdd := manager.AddExistingClient(client)
			require.True(t, isAdd)

			// Start pump goroutines
			go manager.ReadPumpW(client)
			go manager.WritePumpW(client)

			// Subscribe to device channels
			deviceChannels := []string{"device-1", "device-2"}
			err = pubsub.Subscribe(deviceChannels, func(channel string, msg *wspkg.Message) {
				if client.IsConnected() {
					if strings.HasPrefix(msg.GetEvent(), "notify") {
						client.Send(msg)
					}
				}
			})
			require.NoError(t, err)

			// Wait for subscription to be established
			time.Sleep(100 * time.Millisecond)

			// Publish test messages
			testMsg1, err := wspkg.NewMessageFromJSON("notify-test", map[string]interface{}{
				"device_id": 1,
				"message":   "Test notification 1",
			})
			require.NoError(t, err)

			testMsg2, err := wspkg.NewMessageFromJSON("notify-alert", map[string]interface{}{
				"device_id": 2,
				"message":   "Test notification 2",
			})
			require.NoError(t, err)

			// Publish messages to channels
			err = pubsub.Publish("device-1", testMsg1)
			require.NoError(t, err)

			err = pubsub.Publish("device-2", testMsg2)
			require.NoError(t, err)

			// Read messages from WebSocket
			receivedMessages := make([]*wspkg.Message, 0)
			timeout := time.After(2 * time.Second)
			messageCount := 0

			for messageCount < 2 {
				select {
				case <-timeout:
					t.Fatal("Timeout waiting for messages")
				default:
					conn.SetReadDeadline(time.Now().Add(500 * time.Millisecond))
					var msg wspkg.Message
					err := conn.ReadJSON(&msg)
					if err != nil {
						if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway) {
							t.Logf("WebSocket read error: %v", err)
						}
						continue
					}
					receivedMessages = append(receivedMessages, &msg)
					messageCount++
				}
			}

			// Verify received messages
			assert.Len(t, receivedMessages, 2)

			// Check first message
			assert.Equal(t, "notify-test", receivedMessages[0].Event)

			// Check second message
			assert.Equal(t, "notify-alert", receivedMessages[1].Event)

			t.Logf("Successfully received %d notification messages", len(receivedMessages))
		}))
		defer server.Close()

		// Convert HTTP URL to WebSocket URL
		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "?id=1&id=2"

		// Connect to WebSocket
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()

		// Test passes if we reach here without errors
	})

	t.Run("NotifyWS should handle invalid device IDs", func(t *testing.T) {
		e := echo.New()
		req := httptest.NewRequest(http.MethodGet, "/notify-door?id=invalid", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := wsCtx.NotifiyWS(c)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid device ID")
	})

	t.Run("NotifyWS should handle missing device IDs", func(t *testing.T) {
		e := echo.New()
		req := httptest.NewRequest(http.MethodGet, "/notify-door", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := wsCtx.NotifiyWS(c)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "at least one device ID is required")
	})
}

// TestNotifyWS_ClientManagement tests proper client lifecycle management
func TestNotifyWS_ClientManagement(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	pubsub, err := wspkg.NewPubSubManager("redis://localhost:6379")
	require.NoError(t, err)
	defer pubsub.Close()

	_ = wspkg.NewWebSocketCtx(manager, nil, nil, pubsub)

	t.Run("Client should be properly registered and cleaned up", func(t *testing.T) {
		// Create test server
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			upgrader := websocket.Upgrader{
				CheckOrigin: func(r *http.Request) bool { return true },
			}
			conn, err := upgrader.Upgrade(w, r, nil)
			require.NoError(t, err)

			// Create and register client
			client := wspkg.NewClient(conn)
			client.SetOnline(true)

			initialClientCount := manager.GetClientCount()

			isAdd := manager.AddExistingClient(client)
			require.True(t, isAdd)

			// Verify client was added
			assert.Equal(t, initialClientCount+1, manager.GetClientCount())

			// Start pumps
			go manager.ReadPumpW(client)
			go manager.WritePumpW(client)

			// Wait a bit then close
			time.Sleep(100 * time.Millisecond)
			client.Close()

			// Wait for cleanup
			time.Sleep(100 * time.Millisecond)

			// Verify client was removed
			assert.Equal(t, initialClientCount, manager.GetClientCount())
		}))
		defer server.Close()

		// Connect and disconnect
		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "?id=1"
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		conn.Close()
	})
}
