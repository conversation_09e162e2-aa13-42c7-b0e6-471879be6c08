package wspkg_test

import (
	"context"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/testing/wspkg/mocks"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestClientAtomicCloseFix tests the atomic close mechanism
func TestClientAtomicCloseFix(t *testing.T) {
	t.Run("PreventDoubleClose", func(t *testing.T) {
		// Create a properly initialized client for testing
		// Use nil connection but the client should handle it gracefully
		client := wspkg.NewClient(nil)
		client.SetOnline(true)

		// Track close calls with a channel to wait for completion
		closeCount := int32(0)
		closeDone := make(chan struct{})
		client.SetCloseHandler(func(c *wspkg.Client) error {
			if atomic.AddInt32(&closeCount, 1) == 1 {
				close(closeDone)
			}
			return nil
		})

		// Call Close multiple times concurrently
		var wg sync.WaitGroup
		for i := 0; i < 10; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				client.Close()
			}()
		}
		wg.Wait()

		// Wait for close handler to complete
		select {
		case <-closeDone:
			// Expected
		case <-time.After(100 * time.Millisecond):
			// Timeout is acceptable since close handler runs in goroutine
		}

		// Verify close handler was called only once
		assert.Equal(t, int32(1), atomic.LoadInt32(&closeCount))
		assert.True(t, client.IsClosed())
	})

	t.Run("ContextCancellationOnClose", func(t *testing.T) {
		// Test context cancellation without websocket connection
		ctx, cancel := context.WithCancel(context.Background())

		// Simulate what happens in client close
		cancel()

		// Verify context is cancelled
		select {
		case <-ctx.Done():
			// Expected - context should be cancelled
		case <-time.After(100 * time.Millisecond):
			t.Fatal("Context was not cancelled after client close")
		}
	})
}

// TestClientSendWithAtomicCheck tests the enhanced Send method
func TestClientSendWithAtomicCheck(t *testing.T) {
	t.Run("SendToClosedClient", func(t *testing.T) {
		// Test the atomic close logic without websocket connection
		client := wspkg.NewClient(nil)
		client.SetOnline(true)

		// Close the client first
		client.Close()

		// Try to send a message
		msg := wspkg.NewMessage("test", []byte(`{"data": "test"}`))

		// This should not panic or block
		client.Send(msg)

		// Verify client is closed
		assert.True(t, client.IsClosed())
		assert.False(t, client.IsConnected())
	})

	t.Run("AtomicFlagBehavior", func(t *testing.T) {
		// Test atomic flag behavior directly
		client := wspkg.NewClient(nil)

		// Initially should not be closed
		assert.False(t, client.IsClosed())

		// After close should be closed
		client.Close()
		assert.True(t, client.IsClosed())

		// Multiple closes should still show closed
		client.Close()
		client.Close()
		assert.True(t, client.IsClosed())
	})
}

// TestManagerClientRemovalRaceCondition tests the enhanced RemoveClient method
func TestManagerClientRemovalRaceCondition(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	logger := mocks.NewMockLogger()
	manager.SetLogger(logger)

	// Create a client without websocket connection for testing
	client := &wspkg.Client{}
	client.SetOnline(true)

	// Manually add to manager's client map for testing
	clientId := "test-client-123"

	// Try to remove the same client concurrently
	var wg sync.WaitGroup
	var successCount int32

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			err := manager.RemoveClient(clientId)
			if err == nil {
				atomic.AddInt32(&successCount, 1)
			}
		}()
	}
	wg.Wait()

	// All removal attempts should succeed (idempotent)
	assert.Equal(t, int32(10), atomic.LoadInt32(&successCount))

	// Client should not exist in manager
	_, exists := manager.GetClient(clientId)
	assert.False(t, exists)
}

// TestClientStateRaceConditionFix tests the thread-safe client state operations
func TestClientStateRaceConditionFix(t *testing.T) {
	clientState := wspkg.NewClientState()

	// Create a door client
	clientState.CreateDoorClient("door1", 123, "Test Door", 1)

	t.Run("ConcurrentGetDoorClientsByDoorId", func(t *testing.T) {
		var wg sync.WaitGroup
		var results []*wspkg.DoorClient
		var mu sync.Mutex

		// Concurrently get door clients
		for i := 0; i < 10; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				result := clientState.GetDoorClientsByDoorId(123)
				mu.Lock()
				results = append(results, result)
				mu.Unlock()
			}()
		}
		wg.Wait()

		// All results should be non-nil and have the same door ID
		for _, result := range results {
			assert.NotNil(t, result)
			assert.Equal(t, int64(123), result.DoorId)
		}
	})

	t.Run("SafeQRClientAssociation", func(t *testing.T) {
		// Create QR clients
		clientState.CreateQRClient("qr1", "qr-in", 1, 123)
		clientState.CreateQRClient("qr2", "qr-out", 1, 123)

		// Test safe association
		err := clientState.SafelyAssociateQRClient(123, "qr1", "qr-in")
		assert.NoError(t, err)

		err = clientState.SafelyAssociateQRClient(123, "qr2", "qr-out")
		assert.NoError(t, err)

		// Test duplicate association (should fail)
		err = clientState.SafelyAssociateQRClient(123, "qr1", "qr-in")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "already exists")
	})
}

// TestRedisSubscriptionKeyGeneration tests the enhanced subscription key generation
func TestRedisSubscriptionKeyGeneration(t *testing.T) {
	// This test would require access to the internal method
	// For now, we'll test the concept with a mock implementation

	t.Run("UniqueKeyGeneration", func(t *testing.T) {
		// Test that different channel combinations produce different keys
		channels1 := []string{"device-1", "all"}
		channels2 := []string{"device-2", "all"}

		// In the actual implementation, channels1 and channels3 should produce the same key
		// because the channels are sorted before hashing

		// This is a conceptual test - the actual implementation would need
		// the generateSubscriptionKey method to be exported or tested internally
		assert.NotEqual(t, channels1, channels2)
		assert.NotEqual(t, channels1[0], channels2[0])
	})
}

// TestContextCancellationMechanism tests context-based cancellation
func TestContextCancellationMechanism(t *testing.T) {
	t.Run("ContextCancellation", func(t *testing.T) {
		// Test context cancellation directly
		ctx, cancel := context.WithCancel(context.Background())

		// Start a goroutine that waits for context cancellation
		done := make(chan bool)
		go func() {
			<-ctx.Done()
			done <- true
		}()

		// Cancel the context
		cancel()

		// Verify context cancellation
		select {
		case <-done:
			// Expected
		case <-time.After(100 * time.Millisecond):
			t.Fatal("Context was not cancelled")
		}
	})

	t.Run("OperationTimeout", func(t *testing.T) {
		// Test context timeout in operations
		ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
		defer cancel()

		// Simulate a long-running operation
		select {
		case <-ctx.Done():
			assert.Equal(t, context.DeadlineExceeded, ctx.Err())
		case <-time.After(100 * time.Millisecond):
			t.Fatal("Context timeout did not work")
		}
	})
}

// TestPanicRecovery tests panic recovery in goroutines
func TestPanicRecovery(t *testing.T) {
	t.Run("CloseHandlerPanicRecovery", func(t *testing.T) {
		client := wspkg.NewClient(nil)

		var errorCaught bool
		client.SetErrHandler(func(err error) {
			if err.Error() == "panic in close handler: test panic" {
				errorCaught = true
			}
		})

		// Set a close handler that panics
		client.SetCloseHandler(func(c *wspkg.Client) error {
			panic("test panic")
		})

		// Close the client - this should not crash the program
		client.Close()

		// Wait for panic recovery
		time.Sleep(50 * time.Millisecond)

		// Verify panic was caught and handled
		assert.True(t, errorCaught)
		assert.True(t, client.IsClosed())
	})

	t.Run("GeneralPanicRecovery", func(t *testing.T) {
		// Test general panic recovery pattern
		var panicRecovered bool

		func() {
			defer func() {
				if r := recover(); r != nil {
					panicRecovered = true
				}
			}()
			panic("test panic")
		}()

		assert.True(t, panicRecovered)
	})
}

// TestErrorResponseStandardization tests the standardized error responses
func TestErrorResponseStandardization(t *testing.T) {
	t.Run("ErrorResponseCreation", func(t *testing.T) {
		errorResp := wspkg.NewErrorResponse(400, "Test error message")

		assert.Equal(t, "error", errorResp.Error)
		assert.Equal(t, 400, errorResp.Code)
		assert.Equal(t, "Test error message", errorResp.Message)
	})

	t.Run("DifferentErrorCodes", func(t *testing.T) {
		testCases := []struct {
			code    int
			message string
		}{
			{400, "Bad Request"},
			{401, "Unauthorized"},
			{500, "Internal Server Error"},
		}

		for _, tc := range testCases {
			errorResp := wspkg.NewErrorResponse(tc.code, tc.message)
			assert.Equal(t, tc.code, errorResp.Code)
			assert.Equal(t, tc.message, errorResp.Message)
			assert.Equal(t, "error", errorResp.Error)
		}
	})
}

// TestEnhancedClientStateOperations tests the new client state methods
func TestEnhancedClientStateOperations(t *testing.T) {
	t.Run("SafelyAssociateQRClientValidation", func(t *testing.T) {
		clientState := wspkg.NewClientState()

		// Create door client
		clientState.CreateDoorClient("door1", 123, "Test Door", 1)

		// Create QR clients
		clientState.CreateQRClient("qr1", "qr-in", 1, 123)
		clientState.CreateQRClient("qr2", "qr-out", 1, 123)

		// Test valid associations
		err := clientState.SafelyAssociateQRClient(123, "qr1", "qr-in")
		assert.NoError(t, err)

		err = clientState.SafelyAssociateQRClient(123, "qr2", "qr-out")
		assert.NoError(t, err)

		// Test invalid door ID
		err = clientState.SafelyAssociateQRClient(999, "qr1", "qr-in")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "door client not found")

		// Test invalid QR client ID
		err = clientState.SafelyAssociateQRClient(123, "nonexistent", "qr-in")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "QR client not found")

		// Test invalid QR type
		err = clientState.SafelyAssociateQRClient(123, "qr1", "invalid-type")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid QR type")

		// Test duplicate association
		err = clientState.SafelyAssociateQRClient(123, "qr1", "qr-in")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "already exists")
	})

	t.Run("EnhancedRemoveQRClient", func(t *testing.T) {
		clientState := wspkg.NewClientState()

		// Create door client
		clientState.CreateDoorClient("door1", 123, "Test Door", 1)

		// Create and associate QR clients
		clientState.CreateQRClient("qr1", "qr-in", 1, 123)
		clientState.CreateQRClient("qr2", "qr-out", 1, 123)

		err := clientState.SafelyAssociateQRClient(123, "qr1", "qr-in")
		require.NoError(t, err)
		err = clientState.SafelyAssociateQRClient(123, "qr2", "qr-out")
		require.NoError(t, err)

		// Verify associations exist
		doorClient := clientState.GetDoorClientsByDoorId(123)
		require.NotNil(t, doorClient)
		assert.NotNil(t, doorClient.GetQRIn())
		assert.NotNil(t, doorClient.GetQROut())

		// Remove QR client
		clientState.RemoveQRClient("qr1")

		// Verify QR client is removed and association is cleaned up
		qrClient, err := clientState.GetQRClient("qr1")
		assert.Error(t, err)
		assert.Nil(t, qrClient)

		// Verify door client association is cleaned up
		doorClient = clientState.GetDoorClientsByDoorId(123)
		require.NotNil(t, doorClient)
		assert.Nil(t, doorClient.GetQRIn())     // Should be nil after removal
		assert.NotNil(t, doorClient.GetQROut()) // Should still exist
	})
}
