package wspkg

import (
	"testing"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/config"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNewKeyEventWithConfig tests the OnNewKey event handler with proper config
func TestNewKeyEventWithConfig(t *testing.T) {
	t.Run("WebSocketCtx should be initialized with config", func(t *testing.T) {
		// Create manager
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		// Create config with JWT secret
		cfg := &config.Config{
			JWTSecret: "test-jwt-secret-key-for-new-key-generation",
		}

		// Create WebSocket context with config (using nil for components we don't need)
		wsCtx := wspkg.NewWebSocketCtx(manager, nil, nil, nil, cfg)

		// Verify that config is properly set
		assert.NotNil(t, wsCtx.Config, "Config should not be nil")
		assert.Equal(t, "test-jwt-secret-key-for-new-key-generation", wsCtx.Config.JWTSecret, "JWT secret should be set")

		// Verify client state is initialized
		assert.NotNil(t, wsCtx.ClientState, "ClientState should not be nil")

		// Create door client to test basic functionality
		doorClientID := "test-door-client"
		doorID := int64(9)
		wsCtx.ClientState.CreateDoorClient(doorClientID, doorID, "Test Door Lock", 1)

		// Verify door client was created
		doorClient, err := wsCtx.ClientState.GetDoorClient(doorClientID)
		require.NoError(t, err)
		assert.Equal(t, doorID, doorClient.DoorId)

		// Create QR client
		qrClientID := "test-qr-client"
		wsCtx.ClientState.CreateQRClient(qrClientID, "qr-in", 1, doorID)

		// Verify QR client exists
		qrClient, err := wsCtx.ClientState.GetQRClient(qrClientID)
		require.NoError(t, err)
		assert.Equal(t, "qr-in", qrClient.CheckType())

		t.Logf("✅ WebSocketCtx configuration test completed")
		t.Logf("  Config properly initialized: %t", wsCtx.Config != nil)
		t.Logf("  JWT Secret available: %t", wsCtx.Config.JWTSecret != "")
		t.Logf("  Door client created: %s", doorClientID)
		t.Logf("  QR client created: %s", qrClientID)
		t.Logf("  Fix verified: Config is no longer nil")
	})
}

// TestConfigInitialization tests that WebSocketCtx is properly initialized with config
func TestConfigInitialization(t *testing.T) {
	t.Run("WebSocketCtx should be initialized with config", func(t *testing.T) {
		// Create manager
		manager, err := wspkg.DefaultManager()
		require.NoError(t, err)

		// Create config
		cfg := &config.Config{
			JWTSecret:         "test-jwt-secret",
			Port:              ":8080",
			EmergencyPassword: "emergency123",
			MainBranchId:      1,
			JwtExpHour:        24,
		}

		// Create WebSocket context with config (using nil for components we don't need)
		wsCtx := wspkg.NewWebSocketCtx(manager, nil, nil, nil, cfg)

		// Verify all components are properly initialized
		assert.NotNil(t, wsCtx.GetManager(), "Manager should not be nil")
		assert.NotNil(t, wsCtx.ClientState, "ClientState should not be nil")
		assert.NotNil(t, wsCtx.Config, "Config should not be nil")

		// Verify config values
		assert.Equal(t, "test-jwt-secret", wsCtx.Config.JWTSecret, "JWT secret should match")
		assert.Equal(t, ":8080", wsCtx.Config.Port, "Port should match")
		assert.Equal(t, "emergency123", wsCtx.Config.EmergencyPassword, "Emergency password should match")
		assert.Equal(t, 1, wsCtx.Config.MainBranchId, "Main branch ID should match")
		assert.Equal(t, 24, wsCtx.Config.JwtExpHour, "JWT expiration hour should match")

		t.Logf("✅ WebSocketCtx configuration test completed")
		t.Logf("  All components initialized: ✓")
		t.Logf("  Config values verified: ✓")
		t.Logf("  Fix confirmed: Config is properly passed and initialized")
	})
}
