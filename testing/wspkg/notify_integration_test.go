package wspkg_test

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNotifyWS_EndToEndFlow tests the complete end-to-end notification flow
func TestNotifyWS_EndToEndFlow(t *testing.T) {
	// Skip if Redis is not available
	pubsub, err := wspkg.NewPubSubManager("redis://localhost:6379")
	if err != nil {
		t.Skipf("Redis not available, skipping integration test: %v", err)
	}
	defer pubsub.Close()

	// Create WebSocket manager
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	// Create WebSocketCtx
	wsCtx := wspkg.NewWebSocketCtx(manager, nil, nil, pubsub)

	// Register event routes
	wsCtx.WebSocketEventRoute()

	t.Run("Complete notification flow should work", func(t *testing.T) {
		// Create Echo server
		e := echo.New()
		e.GET("/notify-door", wsCtx.NotifiyWS)

		// Start test server
		server := httptest.NewServer(e)
		defer server.Close()

		// Convert to WebSocket URL
		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/notify-door?id=123&id=456"

		// Connect WebSocket client
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()

		// Wait for connection to be established
		time.Sleep(200 * time.Millisecond)

		// Publish test notifications
		testNotifications := []struct {
			channel string
			event   string
			data    map[string]interface{}
		}{
			{
				channel: "device-123",
				event:   "notify-door-open",
				data:    map[string]interface{}{"device_id": 123, "status": "opened"},
			},
			{
				channel: "device-456",
				event:   "notify-alert",
				data:    map[string]interface{}{"device_id": 456, "alert": "unauthorized access"},
			},
			{
				channel: "device-123",
				event:   "notify-status",
				data:    map[string]interface{}{"device_id": 123, "online": true},
			},
		}

		// Publish notifications
		for _, notification := range testNotifications {
			msg, err := wspkg.NewMessageFromJSON(notification.event, notification.data)
			require.NoError(t, err)

			err = pubsub.Publish(notification.channel, msg)
			require.NoError(t, err)

			t.Logf("Published notification: %s to channel: %s", notification.event, notification.channel)
		}

		// Read notifications from WebSocket
		receivedNotifications := make([]wspkg.Message, 0)
		timeout := time.After(3 * time.Second)

		for len(receivedNotifications) < len(testNotifications) {
			select {
			case <-timeout:
				t.Fatalf("Timeout waiting for notifications. Received %d out of %d",
					len(receivedNotifications), len(testNotifications))
			default:
				conn.SetReadDeadline(time.Now().Add(500 * time.Millisecond))
				var msg wspkg.Message
				err := conn.ReadJSON(&msg)
				if err != nil {
					if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway) {
						t.Logf("WebSocket read error: %v", err)
					}
					continue
				}

				receivedNotifications = append(receivedNotifications, msg)
				t.Logf("Received notification: %s", msg.Event)
			}
		}

		// Verify all notifications were received
		assert.Len(t, receivedNotifications, len(testNotifications))

		// Verify notification content
		expectedEvents := []string{"notify-door-open", "notify-alert", "notify-status"}
		receivedEvents := make([]string, len(receivedNotifications))
		for i, notification := range receivedNotifications {
			receivedEvents[i] = notification.Event
		}

		for _, expectedEvent := range expectedEvents {
			assert.Contains(t, receivedEvents, expectedEvent,
				"Expected event %s not found in received events", expectedEvent)
		}

		t.Logf("Successfully received all %d notifications", len(receivedNotifications))
	})

	t.Run("Multiple clients should receive notifications independently", func(t *testing.T) {
		// Create Echo server
		e := echo.New()
		e.GET("/notify-door", wsCtx.NotifiyWS)

		server := httptest.NewServer(e)
		defer server.Close()

		// Connect multiple clients
		clients := make([]*websocket.Conn, 2)
		for i := 0; i < 2; i++ {
			wsURL := fmt.Sprintf("ws%s/notify-door?id=%d",
				strings.TrimPrefix(server.URL, "http"), 100+i)

			conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
			require.NoError(t, err)
			defer conn.Close()

			clients[i] = conn
		}

		// Wait for connections
		time.Sleep(200 * time.Millisecond)

		// Publish notifications to different devices
		notifications := []struct {
			channel string
			event   string
		}{
			{"device-100", "notify-test-1"},
			{"device-101", "notify-test-2"},
		}

		for _, notification := range notifications {
			msg, err := wspkg.NewMessageFromJSON(notification.event,
				map[string]interface{}{"test": true})
			require.NoError(t, err)

			err = pubsub.Publish(notification.channel, msg)
			require.NoError(t, err)
		}

		// Each client should receive only their notification
		for i, client := range clients {
			client.SetReadDeadline(time.Now().Add(2 * time.Second))
			var msg wspkg.Message
			err := client.ReadJSON(&msg)
			require.NoError(t, err)

			expectedEvent := fmt.Sprintf("notify-test-%d", i+1)
			assert.Equal(t, expectedEvent, msg.Event)

			t.Logf("Client %d received correct notification: %s", i, msg.Event)
		}
	})
}

// TestNotifyWS_ErrorHandling tests error scenarios
func TestNotifyWS_ErrorHandling(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	pubsub, err := wspkg.NewPubSubManager("redis://localhost:6379")
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer pubsub.Close()

	wsCtx := wspkg.NewWebSocketCtx(manager, nil, nil, pubsub)

	t.Run("Should handle invalid device IDs gracefully", func(t *testing.T) {
		e := echo.New()
		req := httptest.NewRequest(http.MethodGet, "/notify-door?id=invalid&id=123", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := wsCtx.NotifiyWS(c)
		assert.Error(t, err)
		// The function should return an error for invalid device IDs
		assert.Contains(t, err.Error(), "invalid device ID")
	})

	t.Run("Should handle empty device ID list", func(t *testing.T) {
		e := echo.New()
		req := httptest.NewRequest(http.MethodGet, "/notify-door", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := wsCtx.NotifiyWS(c)
		assert.Error(t, err)
		// The function should return an error for missing device IDs
		assert.Contains(t, err.Error(), "at least one device ID is required")
	})
}

// TestNotifyWS_MessageFiltering tests that only notify events are forwarded
func TestNotifyWS_MessageFiltering(t *testing.T) {
	pubsub, err := wspkg.NewPubSubManager("redis://localhost:6379")
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer pubsub.Close()

	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	wsCtx := wspkg.NewWebSocketCtx(manager, nil, nil, pubsub)

	t.Run("Should only forward notify events", func(t *testing.T) {
		e := echo.New()
		e.GET("/notify-door", wsCtx.NotifiyWS)

		server := httptest.NewServer(e)
		defer server.Close()

		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/notify-door?id=999"
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()

		time.Sleep(200 * time.Millisecond)

		// Publish mixed events
		events := []struct {
			event      string
			shouldPass bool
		}{
			{"notify-test", true},
			{"notification", true},
			{"isOpen", false},
			{"status", false},
			{"notify-alert", true},
			{"heartbeat", false},
		}

		for _, event := range events {
			msg, err := wspkg.NewMessageFromJSON(event.event, map[string]interface{}{"test": true})
			require.NoError(t, err)

			err = pubsub.Publish("device-999", msg)
			require.NoError(t, err)
		}

		// Count received messages
		receivedCount := 0
		timeout := time.After(2 * time.Second)

		for {
			select {
			case <-timeout:
				// Expected to receive only notify events
				expectedCount := 0
				for _, event := range events {
					if event.shouldPass {
						expectedCount++
					}
				}
				assert.Equal(t, expectedCount, receivedCount,
					"Should receive only notify events")
				return
			default:
				conn.SetReadDeadline(time.Now().Add(100 * time.Millisecond))
				var msg wspkg.Message
				err := conn.ReadJSON(&msg)
				if err != nil {
					continue
				}

				// Verify it's a notify event
				assert.True(t, strings.HasPrefix(msg.Event, "notify") || msg.Event == "notification",
					"Received non-notify event: %s", msg.Event)
				receivedCount++
			}
		}
	})
}
