package wspkg

import (
	"encoding/json"
	"strconv"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestDoorHTTPIntegration tests the HTTP endpoint integration with connected clients
func TestDoorHTTPIntegration(t *testing.T) {
	// Test data matching user's request
	doorID := int64(9)
	branchID := 1
	apiKey := "PvLc_9yb_gUfJ4C6Zc6vSBbGOyBeSycjsyhbPL_JDGM="
	jwtSecret := "test-jwt-secret-key"

	t.Run("HTTP OpenDoor Request with Connected Clients", func(t *testing.T) {
		// Step 1: Setup client state (simulating connected WebSocket clients)
		clientState := wspkg.NewClientState()

		// Step 2: Create door client (simulating /ws-auth connection)
		doorClientID := "door-client-9"
		clientState.CreateDoorClient(doorClientID, doorID, "Test Door Lock 9", branchID)

		// Step 3: Create QR clients (simulating /ws-qr-auth connections)
		qrInClientID := "qr-in-client-9"
		qrOutClientID := "qr-out-client-9"

		clientState.CreateQRClientWithDoorCheckin(qrInClientID, "qr-in", branchID, doorID)
		clientState.CreateQRClientWithDoorCheckin(qrOutClientID, "qr-out", branchID, doorID)

		// Step 4: Generate QR code token (simulating QR code generation)
		qrToken := generateQRCodeToken(t, qrInClientID, doorID, branchID, jwtSecret)

		// Step 5: Test HTTP OpenDoor request structure
		testOpenDoorRequestStructure(t, qrToken, apiKey)

		// Step 6: Validate QR token and client state integration
		validateTokenAndClientState(t, qrToken, doorClientID, qrInClientID, clientState, jwtSecret)

		// Step 7: Simulate door open/close flow with connected clients
		simulateConnectedDoorFlow(t, clientState, doorClientID, doorID)

		t.Logf("✅ HTTP OpenDoor integration with connected clients successful")
		t.Logf("  Door Client: %s (connected)", doorClientID)
		t.Logf("  QR-In Client: %s (connected)", qrInClientID)
		t.Logf("  QR-Out Client: %s (connected)", qrOutClientID)
		t.Logf("  API Key: %s", apiKey[:10]+"...")
		t.Logf("  QR Token: %s", qrToken[:20]+"...")
	})
}

// generateQRCodeToken generates a JWT token for QR code access
func generateQRCodeToken(t *testing.T, qrClientID string, doorID int64, branchID int, jwtSecret string) string {
	qrInfo := utils.QRCodeInfo{
		ClientId:    qrClientID,
		ReqDeviceId: strconv.FormatInt(doorID, 10),
		ConnType:    "qr-in",
		BranchId:    branchID,
		GenTime:     time.Now(),
		ExpTime:     time.Now().Add(5 * time.Minute),
	}

	token, err := utils.GenerateJWTToken(qrInfo, 5*time.Minute, []byte(jwtSecret))
	require.NoError(t, err, "Should generate QR token successfully")

	t.Logf("✅ QR code token generated: %s", token[:20]+"...")
	return token
}

// testOpenDoorRequestStructure tests the HTTP OpenDoor request structure
func testOpenDoorRequestStructure(t *testing.T, qrToken, apiKey string) {
	// Create the exact HTTP request structure for OpenDoor endpoint
	doorAccessReq := types.DoorLockAccessReq{
		User: types.TmpUser{
			ID:       123,
			BookType: "employee",
		},
		QRcode:    qrToken,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	// Test JSON marshaling
	reqBody, err := json.Marshal(doorAccessReq)
	require.NoError(t, err, "Should marshal door access request")

	// Verify request structure
	var unmarshaled types.DoorLockAccessReq
	err = json.Unmarshal(reqBody, &unmarshaled)
	require.NoError(t, err, "Should unmarshal door access request")
	assert.Equal(t, doorAccessReq, unmarshaled, "Marshaled and unmarshaled should match")

	// Validate request fields
	assert.Equal(t, 123, doorAccessReq.User.ID, "User ID should be 123")
	assert.Equal(t, "employee", doorAccessReq.User.BookType, "BookType should be employee")
	assert.Equal(t, qrToken, doorAccessReq.QRcode, "QR code should match")
	assert.NotEmpty(t, doorAccessReq.Timestamp, "Timestamp should not be empty")

	t.Logf("✅ HTTP OpenDoor request structure validated")
	t.Logf("  Request JSON: %s", string(reqBody))
	t.Logf("  User ID: %d", doorAccessReq.User.ID)
	t.Logf("  BookType: %s", doorAccessReq.User.BookType)
	t.Logf("  QR Token: %s", qrToken[:20]+"...")
}

// validateTokenAndClientState validates QR token and client state integration
func validateTokenAndClientState(t *testing.T, qrToken, doorClientID, qrClientID string, clientState *wspkg.ClientState, jwtSecret string) {
	// Step 1: Validate QR token structure
	claims, err := utils.ValidateToken(qrToken, []byte(jwtSecret))
	require.NoError(t, err, "QR token should be valid")

	qrData := claims.QRInfo
	assert.Equal(t, qrClientID, qrData.ClientId, "Client ID should match")
	assert.Equal(t, "9", qrData.ReqDeviceId, "Device ID should match")
	assert.Equal(t, "qr-in", qrData.ConnType, "Connection type should be qr-in")

	// Step 2: Verify door client exists and is accessible
	doorClient, err := clientState.GetDoorClient(doorClientID)
	require.NoError(t, err, "Door client should exist")
	assert.Equal(t, int64(9), doorClient.DoorId, "Door ID should match")
	assert.Equal(t, "Test Door Lock 9", doorClient.Name, "Door name should match")

	// Step 3: Verify QR client exists and is associated with door
	qrClient, err := clientState.GetQRClient(qrClientID)
	require.NoError(t, err, "QR client should exist")
	assert.Equal(t, "qr-in", qrClient.CheckType(), "QR client type should match")

	// Step 4: Verify door-QR client relationship
	qrInConnected, qrOutConnected, exists := clientState.GetDoorQRStatus(int64(9))
	assert.True(t, exists, "Door should exist in client state")
	assert.True(t, qrInConnected, "QR-in should be connected")
	assert.True(t, qrOutConnected, "QR-out should be connected")

	t.Logf("✅ Token and client state integration validated")
	t.Logf("  QR token claims validated")
	t.Logf("  Door client accessible: %s", doorClientID)
	t.Logf("  QR client accessible: %s", qrClientID)
	t.Logf("  Door-QR relationship confirmed")
}

// simulateConnectedDoorFlow simulates door operations with connected clients
func simulateConnectedDoorFlow(t *testing.T, clientState *wspkg.ClientState, doorClientID string, doorID int64) {
	// Get door client
	doorClient, err := clientState.GetDoorClient(doorClientID)
	require.NoError(t, err, "Should get door client")

	// Test initial state
	assert.False(t, doorClient.IsOpen(), "Door should initially be closed")

	// Simulate HTTP OpenDoor endpoint processing:
	// 1. QR token validated ✓ (done in previous step)
	// 2. Door client found ✓ (done in previous step)
	// 3. Door open command sent to hardware (simulated)
	t.Log("🚪 Simulating door open command to hardware...")

	// 4. Door state updated after hardware confirmation
	doorClient.SetIsOpen(true)
	assert.True(t, doorClient.IsOpen(), "Door should be open after hardware confirmation")

	// 5. Attendance record would be created (simulated)
	t.Log("📝 Simulating attendance record creation...")

	// 6. Notifications sent to connected clients (simulated)
	t.Log("🔔 Simulating notifications to connected QR clients...")

	// Verify QR client states during door operation
	qrInConnected, qrOutConnected, exists := clientState.GetDoorQRStatus(doorID)
	assert.True(t, exists, "Door should exist")
	assert.True(t, qrInConnected, "QR-in should remain connected during operation")
	assert.True(t, qrOutConnected, "QR-out should remain connected during operation")

	// Simulate door close after timeout or manual close
	t.Log("🚪 Simulating door close after timeout...")
	doorClient.SetIsOpen(false)
	assert.False(t, doorClient.IsOpen(), "Door should be closed after timeout")

	t.Logf("✅ Connected door flow simulation completed")
	t.Logf("  Door operations: open → closed")
	t.Logf("  QR clients remained connected throughout")
	t.Logf("  Attendance record creation simulated")
	t.Logf("  Client notifications simulated")
}

// TestHTTPEndpointFlow tests the complete HTTP endpoint flow
func TestHTTPEndpointFlow(t *testing.T) {
	t.Run("Complete HTTP Endpoint Flow Validation", func(t *testing.T) {
		// Test the complete flow that would happen when HTTP /opendoor is called
		// with connected WebSocket clients

		doorID := int64(9)
		branchID := 1
		apiKey := "PvLc_9yb_gUfJ4C6Zc6vSBbGOyBeSycjsyhbPL_JDGM="

		// Step 1: Validate WebSocket authentication requests (from previous test)
		doorAuthReq := types.WSConnectReq{
			ConnType: "door-lock",
			Id:       int(doorID),
			APIKey:   apiKey,
			BranchId: branchID,
		}

		qrInAuthReq := types.QRConnectReq{
			ConnType:    "qr-in",
			ReqDeviceId: strconv.FormatInt(doorID, 10),
			BranchId:    branchID,
		}

		qrOutAuthReq := types.QRConnectReq{
			ConnType:    "qr-out",
			ReqDeviceId: strconv.FormatInt(doorID, 10),
			BranchId:    branchID,
		}

		// Validate all auth requests can be marshaled
		doorAuthBody, err := json.Marshal(doorAuthReq)
		require.NoError(t, err)

		qrInAuthBody, err := json.Marshal(qrInAuthReq)
		require.NoError(t, err)

		qrOutAuthBody, err := json.Marshal(qrOutAuthReq)
		require.NoError(t, err)

		// Step 2: Validate HTTP OpenDoor request structure
		qrToken := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token" // Mock token for structure test

		doorAccessReq := types.DoorLockAccessReq{
			User: types.TmpUser{
				ID:       123,
				BookType: "employee",
			},
			QRcode:    qrToken,
			Timestamp: time.Now().Format(time.RFC3339),
		}

		doorAccessBody, err := json.Marshal(doorAccessReq)
		require.NoError(t, err)

		t.Logf("✅ Complete HTTP endpoint flow validation successful")
		t.Logf("  Door auth request: %s", string(doorAuthBody))
		t.Logf("  QR-in auth request: %s", string(qrInAuthBody))
		t.Logf("  QR-out auth request: %s", string(qrOutAuthBody))
		t.Logf("  HTTP OpenDoor request: %s", string(doorAccessBody))
		t.Logf("  All requests properly structured for endpoints")
	})
}
